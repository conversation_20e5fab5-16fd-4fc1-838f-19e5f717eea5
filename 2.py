import os
import sys
import json
import time
import random
import string
import subprocess
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from pathlib import Path
import hashlib
import base64
import requests
from datetime import datetime
import csv
import pyotp
import logging
from seleniumbase import Driver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.support.ui import Select
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException
import psutil
import signal

class GmailAutomation:
    def __init__(self, basedir):
        self.basedir = Path(basedir)
        self.setup_directories()
        
    def setup_directories(self):
        """Create necessary directories"""
        dirs = [
            self.basedir / 'log',
            self.basedir / 'log' / 'accounts',
            self.basedir / 'log' / 'accountNotOKimage',
            self.basedir / 'log' / 'accountNotOKHtml',
            self.basedir / 'jsonfiles',
            self.basedir / 'profiles',
            self.basedir / 'downloaded_files' / 'proxy_ext_dir'
        ]

        # Remove existing log directory
        if (self.basedir / 'log').exists():
            import shutil
            shutil.rmtree(self.basedir / 'log')

        for dir_path in dirs:
            dir_path.mkdir(parents=True, exist_ok=True)

        # Create empty log files
        (self.basedir / 'log' / 'accountOK.txt').touch()
        (self.basedir / 'log' / 'accountNotOK.txt').touch()

    def create_proxy_auth_extension(self, account):
        """Create a dynamic proxy auth extension with account-specific credentials"""
        try:
            # Create unique extension directory for this account
            ext_dir = self.basedir / 'downloaded_files' / 'proxy_ext_dir' / account['email'].replace('@', '_').replace('.', '_')
            ext_dir.mkdir(parents=True, exist_ok=True)

            # Read template files
            template_manifest = self.basedir / 'proxy_auth_extension' / 'manifest.json'
            template_background = self.basedir / 'proxy_auth_extension' / 'background.js'
            template_content = self.basedir / 'proxy_auth_extension' / 'content.js'

            # Copy manifest.json
            with open(template_manifest, 'r') as f:
                manifest_content = f.read()
            with open(ext_dir / 'manifest.json', 'w') as f:
                f.write(manifest_content)

            # Create background.js with actual credentials
            with open(template_background, 'r') as f:
                background_content = f.read()

            # Replace placeholders with actual credentials
            background_content = background_content.replace(
                'PLACEHOLDER_USERNAME', account.get('proxyUsername', '')
            ).replace(
                'PLACEHOLDER_PASSWORD', account.get('proxyPassword', '')
            )

            with open(ext_dir / 'background.js', 'w') as f:
                f.write(background_content)

            # Copy content.js
            with open(template_content, 'r') as f:
                content_content = f.read()
            with open(ext_dir / 'content.js', 'w') as f:
                f.write(content_content)

            self.save_log(account['email'], f"Created proxy auth extension at: {ext_dir}")
            return str(ext_dir)

        except Exception as e:
            self.save_log(account['email'], f"Failed to create proxy auth extension: {e}")
            return None

    def create_driver(self, account):
        """Create Chrome driver with seleniumbase - FIXED PROXY ISSUE"""
        profile_dir = self.basedir / 'profiles' / account['email']
        profile_dir.mkdir(parents=True, exist_ok=True)

        try:
            # Create proxy auth extension with account-specific credentials
            extension_path = None
            proxy_string = None

            if account.get('proxyUsername') and account.get('proxyPassword') and account.get('proxy') and account.get('port'):
                # Create dynamic proxy auth extension
                extension_path = self.create_proxy_auth_extension(account)

                # Format proxy string for seleniumbase
                proxy_ip = account['proxy'].replace(',', '.')
                proxy_string = f"{proxy_ip}:{account['port']}"
                print(f"[{account['email']}] Using proxy: {proxy_string} with auth extension: {extension_path}")

            # Create driver with options
            driver_kwargs = {
                'uc': True,  # Use undetected mode
                'headless': False,
                'user_data_dir': str(profile_dir),
                'disable_gpu': False,
                'incognito': False,
                'guest_mode': False,
                'disable_csp': True,
                'disable_ws': True,
                'enable_ws': False,
                'block_images': False,
                'do_not_track': True,
                'disable_notifications': True,
                'disable_popup_blocking': True
            }

            # Add proxy configuration
            if proxy_string:
                driver_kwargs['proxy'] = proxy_string

            # Add extension if created
            if extension_path:
                driver_kwargs['extension_dir'] = extension_path
                self.save_log(account['email'], f"Loading proxy auth extension from: {extension_path}")

            # Additional Chrome options for proxy handling
            chrome_options = []
            if proxy_string:
                chrome_options.extend([
                    '--proxy-server=' + proxy_string,
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor',
                    '--ignore-certificate-errors',
                    '--ignore-ssl-errors',
                    '--ignore-certificate-errors-spki-list',
                    '--disable-extensions-except=' + (extension_path if extension_path else ''),
                    '--load-extension=' + (extension_path if extension_path else '')
                ])

            if chrome_options:
                driver_kwargs['chrome_options'] = chrome_options

            driver = Driver(**driver_kwargs)

            # Verify proxy is working
            if proxy_string:
                self.save_log(account['email'], "Driver created with proxy configuration")

            return driver

        except Exception as e:
            self.save_log(account['email'], f"Failed to create driver: {e}")
            # Try fallback without proxy if proxy configuration failed
            if proxy_string and "proxy" in str(e).lower():
                self.save_log(account['email'], "Attempting fallback without proxy...")
                try:
                    fallback_kwargs = {
                        'uc': True,
                        'headless': False,
                        'user_data_dir': str(profile_dir),
                        'disable_gpu': False,
                        'incognito': False,
                        'guest_mode': False,
                        'disable_csp': True,
                        'disable_ws': True,
                        'enable_ws': False,
                        'block_images': False,
                        'do_not_track': True
                    }
                    driver = Driver(**fallback_kwargs)
                    self.save_log(account['email'], "Fallback driver created without proxy")
                    return driver
                except Exception as fallback_e:
                    self.save_log(account['email'], f"Fallback driver creation also failed: {fallback_e}")
            return None

    def verify_proxy_connection(self, driver, account):
        """Verify proxy connection is working"""
        try:
            if not account.get('proxy'):
                return True

            self.save_log(account['email'], "Verifying proxy connection...")

            # Try to access a simple IP check service
            for attempt in range(3):
                try:
                    driver.get('http://httpbin.org/ip')
                    self.sleep(5)

                    # Check if page loaded successfully
                    page_source = driver.page_source.lower()
                    if 'origin' in page_source or 'ip' in page_source:
                        self.save_log(account['email'], "Proxy connection verified successfully")
                        return True
                    else:
                        self.save_log(account['email'], f"Proxy verification attempt {attempt + 1} failed")

                except Exception as e:
                    self.save_log(account['email'], f"Proxy verification attempt {attempt + 1} error: {e}")

                self.sleep(3)

            self.save_log(account['email'], "Proxy connection verification failed")
            return False

        except Exception as e:
            self.save_log(account['email'], f"Proxy verification error: {e}")
            return False

    def handle_proxy_auth_failure(self, driver, account):
        """Handle proxy authentication failures"""
        try:
            self.save_log(account['email'], "Handling proxy authentication failure...")

            # Check for authentication dialogs
            auth_dialogs = [
                "//div[contains(text(), 'proxy')]",
                "//div[contains(text(), 'authentication')]",
                "//input[@type='password']",
                "//dialog[@open]"
            ]

            for xpath in auth_dialogs:
                try:
                    elements = driver.find_elements(By.XPATH, xpath)
                    if elements:
                        self.save_log(account['email'], f"Found auth dialog: {xpath}")

                        # Try to close the dialog
                        close_buttons = driver.find_elements(By.XPATH, "//button[contains(text(), 'Cancel') or contains(text(), 'Close')]")
                        if close_buttons:
                            close_buttons[0].click()
                            self.save_log(account['email'], "Closed auth dialog")
                            return True

                except Exception as e:
                    continue

            # Press Escape to close any modal dialogs
            try:
                from selenium.webdriver.common.keys import Keys
                driver.find_element(By.TAG_NAME, 'body').send_keys(Keys.ESCAPE)
                self.save_log(account['email'], "Sent Escape key to close dialogs")
            except:
                pass

            return False

        except Exception as e:
            self.save_log(account['email'], f"Error handling proxy auth failure: {e}")
            return False

    def save_log(self, email, message):
        """Save log message to account-specific file"""
        try:
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            log_file = self.basedir / 'log' / 'accounts' / f'{email}.txt'
            
            with open(log_file, 'a', encoding='utf-8') as f:
                f.write(f"{timestamp} : {message}\n")
                
        except Exception as e:
            print(f"Error saving log: {e}")

    def get_element(self, driver, xpath, timeout=10):
        """Get element by XPath with timeout"""
        try:
            current_email = getattr(driver, '_current_email', 'unknown')
            self.save_log(current_email, f"wait [{timeout}] for {xpath}")
            
            element = WebDriverWait(driver, timeout).until(
                EC.element_to_be_clickable((By.XPATH, xpath))
            )
            elements = driver.find_elements(By.XPATH, xpath)
            self.save_log(current_email, f"elements count {len(elements)}")
            return element
        except TimeoutException:
            current_email = getattr(driver, '_current_email', 'unknown')
            self.save_log(current_email, "not exist")
            return None

    def get_all_elements(self, driver, xpath, timeout=10):
        """Get all elements by XPath"""
        try:
            WebDriverWait(driver, timeout).until(
                EC.presence_of_element_located((By.XPATH, xpath))
            )
            return driver.find_elements(By.XPATH, xpath)
        except TimeoutException:
            return []

    def sleep(self, seconds):
        """Sleep for specified seconds"""
        time.sleep(seconds)

    def random_integer(self, min_val, max_val):
        """Generate random integer"""
        return random.randint(min_val, max_val)

    def shuffle_list(self, lst):
        """Shuffle list"""
        new_list = lst.copy()
        random.shuffle(new_list)
        return new_list

    def login_action(self, driver, account):
        """Main login function"""
        try:
            # Store email in driver for logging
            driver._current_email = account['email']
            
            email = account['email']
            password = account['password']
            recovery = account['recovry']
            login_status = False

            # Verify proxy connection first
            if account.get('proxy'):
                if not self.verify_proxy_connection(driver, account):
                    self.save_log(email, "Proxy connection failed, attempting to handle auth issues...")
                    self.handle_proxy_auth_failure(driver, account)

            # Check IP
            for j in range(5):
                try:
                    self.save_log(email, f"open [{j}] http://ipinfo.io/json {account['proxy'].replace(',', '.')}")
                    driver.get('http://ipinfo.io/json')

                    # Check for proxy auth dialogs after navigation
                    if account.get('proxy'):
                        self.handle_proxy_auth_failure(driver, account)

                    break
                except Exception as e:
                    self.save_log(email, str(e))
                    # Handle proxy auth issues
                    if account.get('proxy') and ('proxy' in str(e).lower() or 'auth' in str(e).lower()):
                        self.handle_proxy_auth_failure(driver, account)
                self.sleep(5)

            # Navigate to Gmail login
            try:
                driver.get('https://accounts.google.com/signin/v2/identifier?continue=https%3A%2F%2Fmail.google.com%2Fmail%2F&service=mail&sacu=1&rip=1&flowName=GlifWebSignIn&flowEntry=ServiceLogin')
                self.sleep(10)

                # Check if already logged in
                for i in range(6):
                    url = driver.current_url
                    self.save_log(email, f"check url {url}")
                    
                    if "mail.google.com/mail" in url and "accounts.google.com" not in url:
                        login_status = True
                        break

                    login_element = self.get_element(driver, "//input[@id='identifierId']", 2)
                    if login_element:
                        break

                    if "https://accounts.google.com/v3/signin/confirmidentifier" in url:
                        break

                    self.sleep(5)

                if login_status:
                    self.handle_post_login_dialogs(driver, account)
                    return True

                # Enter email
                login_element = self.get_element(driver, "//input[@id='identifierId']", 10)
                if login_element:
                    login_element.clear()
                    login_element.send_keys(email)
                    self.sleep(2)

                # Click Next button for email
                next_btn = self.get_element(driver, "//div[@id='identifierNext']", 10)
                if next_btn:
                    next_btn.click()
                    self.sleep(7)

                # Enter password
                pass_input = self.get_element(driver, "//input[@name='Passwd' or @name='password']", 30)
                if pass_input:
                    self.sleep(3)
                    pass_input.clear()
                    pass_input.send_keys(password)
                    self.sleep(5)

                # Click Next for password
                next_pass = self.get_element(driver, "//div[@id='passwordNext']", 10)
                if next_pass:
                    next_pass.click()
                    self.sleep(5)

                # Handle 2FA
                totp_element = self.get_element(driver, "//input[@id='totpPin']", 5)
                if totp_element:
                    if '2fa_key' in account and account['2fa_key']:
                        fa_code = pyotp.TOTP(account['2fa_key']).now()
                        self.save_log(email, f"2FA Code: {fa_code}")
                        totp_element.clear()
                        totp_element.send_keys(fa_code)
                        self.sleep(2)
                        
                        next_button = self.get_element(driver, "//button[@type='button']//span[contains(text(),'Next')]", 5)
                        if next_button:
                            next_button.click()
                        self.sleep(5)

                # Handle recovery email challenge
                recovery_em = self.get_element(driver, "//div[@role='link' and @data-challengetype='12']", 3)
                if recovery_em:
                    recovery_em.click()
                    self.sleep(5)
                    
                    recovery_email = self.get_element(driver, "//input[@type='email']", 10)
                    if recovery_email:
                        recovery_email.clear()
                        recovery_email.send_keys(recovery)
                        self.sleep(1)
                        recovery_email.send_keys(Keys.ENTER)
                        self.sleep(7)

                self.sleep(10)

                # Handle various prompts
                save_btn = self.get_element(driver, "//button//span[contains(text(),'Save')]", 2)
                if save_btn:
                    save_btn.click()
                    self.sleep(5)

                confirm_btn = self.get_element(driver, "//span[contains(text(),'Confirm')]", 5)
                if confirm_btn:
                    confirm_btn.click()
                    self.sleep(3)

                not_now_btn = self.get_element(driver, "//button//span[contains(text(),'Not now') or contains(text(),'Ahora no') or contains(text(),'Pas maintenant')]/..", 2)
                if not_now_btn:
                    not_now_btn.click()
                    self.sleep(10)

                # Check final login status
                url = driver.current_url
                if "mail.google.com/mail" in url and "accounts.google.com" not in url:
                    login_status = True

            except Exception as e:
                self.save_log(email, f"Login error: {str(e)}")

            if login_status:
                # Handle post-login dialogs
                self.handle_post_login_dialogs(driver, account)
                self.change_language(driver, account)
                self.save_log(email, f"{email} login successfully")

                # Log successful login
                with open(self.basedir / 'log' / 'accountLoginOK.txt', 'a') as f:
                    f.write(f"{account['email']},{account['password']},{account['proxy']},{account['port']},{account['proxyUsername']},{account['proxyPassword']},{account['recovry']},\n")

                return True
            else:
                self.save_log(email, f"{email} NOT LOGIN")
                self.handle_login_failure(driver, account)

        except Exception as e:
            self.save_log(email, f"Login action error: {str(e)}")

        return False

    def handle_post_login_dialogs(self, driver, account):
        """Handle various dialogs that appear after login"""
        try:
            # Close alert dialogs
            close_btn = self.get_element(driver, '//div[@role="alertdialog"]//div[@role="button"]', 2)
            if close_btn:
                close_btn.click()
                self.sleep(1)

            # Handle "Set a home address" dialog
            home_address_dialog = self.get_element(driver, "//div[contains(text(),'Set a home address')]", 3)
            if home_address_dialog:
                self.save_log(account['email'], "Found 'Set a home address' dialog")
                
                # Option 1: Click Skip button
                skip_btn = self.get_element(driver, "//button[contains(text(),'Skip')]", 5)
                if skip_btn:
                    self.save_log(account['email'], "Clicking Skip button for home address")
                    skip_btn.click()
                    self.sleep(3)
                else:
                    # Option 2: Fill address and save (if Skip not found)
                    address_input = self.get_element(driver, "//input[@aria-label='Home address']", 2)
                    if address_input:
                        self.save_log(account['email'], "Filling home address")
                        # You can customize this address
                        fake_address = "123 Main St, New York, NY 10001"
                        address_input.clear()
                        address_input.send_keys(fake_address)
                        self.sleep(2)
                        
                        # Click Save
                        save_btn = self.get_element(driver, "//button[contains(text(),'Save')]", 2)
                        if save_btn:
                            save_btn.click()
                            self.sleep(3)

            # Handle smart features dialog
            smart_features = self.get_element(driver, '//div[@role="dialog"]//div[contains(text(),"Turn off smart features")]/..', 2)
            if smart_features:
                smart_features.click()
                self.sleep(3)
                
                next_btn = self.get_element(driver, '//button[@name="data_consent_dialog_next"]', 2)
                if next_btn:
                    next_btn.click()
                    self.sleep(3)
                
                turn_off_btn = self.get_element(driver, '//button[@name="turn_off_in_product"]', 2)
                if turn_off_btn:
                    turn_off_btn.click()
                    self.sleep(3)
                    driver.get('https://mail.google.com/mail/u/0/#inbox')
                    self.sleep(10)

            # Handle "Verify your phone number" dialog
            phone_dialog = self.get_element(driver, "//div[contains(text(),'Verify your phone number')]", 2)
            if phone_dialog:
                self.save_log(account['email'], "Found phone verification dialog")
                skip_phone_btn = self.get_element(driver, "//button[contains(text(),'Skip') or contains(text(),'Not now')]", 3)
                if skip_phone_btn:
                    self.save_log(account['email'], "Skipping phone verification")
                    skip_phone_btn.click()
                    self.sleep(3)

            # Handle "Add recovery phone" dialog
            recovery_phone_dialog = self.get_element(driver, "//div[contains(text(),'Add recovery phone')]", 2)
            if recovery_phone_dialog:
                self.save_log(account['email'], "Found recovery phone dialog")
                skip_recovery_btn = self.get_element(driver, "//button[contains(text(),'Skip') or contains(text(),'Not now')]", 3)
                if skip_recovery_btn:
                    self.save_log(account['email'], "Skipping recovery phone")
                    skip_recovery_btn.click()
                    self.sleep(3)

            # Handle "Turn on 2-Step Verification" dialog
            two_step_dialog = self.get_element(driver, "//div[contains(text(),'Turn on 2-Step Verification')]", 2)
            if two_step_dialog:
                self.save_log(account['email'], "Found 2-Step Verification dialog")
                skip_2step_btn = self.get_element(driver, "//button[contains(text(),'Skip') or contains(text(),'Not now')]", 3)
                if skip_2step_btn:
                    self.save_log(account['email'], "Skipping 2-Step Verification")
                    skip_2step_btn.click()
                    self.sleep(3)

            # Close any remaining dialogs
            close_btn = self.get_element(driver, '//div[@role="dialog"]//button[@aria-label="Close"]', 1)
            if close_btn:
                close_btn.click()
                self.sleep(1)

            # Handle additional close buttons
            close_btns = [
                '//button[@aria-label="Close"]',
                '//button[contains(text(),"Close")]',
                '//div[@role="button" and @aria-label="Close"]',
                '//span[contains(text(),"Close")]/..'
            ]
            
            for close_xpath in close_btns:
                close_btn = self.get_element(driver, close_xpath, 1)
                if close_btn:
                    close_btn.click()
                    self.sleep(1)
                    break

        except Exception as e:
            self.save_log(account['email'], f"Post-login dialog error: {str(e)}")

    def handle_login_failure(self, driver, account):
        """Handle login failure logging and screenshots"""
        try:
            email = account['email']
            
            # Log failed login
            with open(self.basedir / 'log' / 'accountLoginNotOK.txt', 'a') as f:
                f.write(f"{account['email']},{account['password']},{account['proxy']},{account['port']},{account['proxyUsername']},{account['proxyPassword']},{account['recovry']}\n")
            
            # Save HTML and screenshot
            html_file = self.basedir / 'log' / 'accountNotOKHtml' / f'{email}.txt'
            with open(html_file, 'w', encoding='utf-8') as f:
                f.write(driver.page_source)
            
            screenshot_file = self.basedir / 'log' / 'accountNotOKimage' / f'{email}.png'
            driver.save_screenshot(str(screenshot_file))

        except Exception as e:
            self.save_log(account['email'], f"Login failure handling error: {str(e)}")

    def change_language(self, driver, account):
        """Change Gmail language to English"""
        try:
            element = self.get_element(driver, "//input[@name='q']", 2)
            if element:
                compose_btn = self.get_element(driver, "//div[@role='button' and contains(text(),'Compose')]", 2)
                if not compose_btn:
                    driver.get('https://mail.google.com/mail/u/0/#settings/general')
                    self.sleep(20)
                    
                    lang_select = self.get_element(driver, "//select[1]", 20)
                    if lang_select:
                        self.sleep(3)
                        select = Select(lang_select)
                        select.select_by_value('en')
                        self.sleep(3)
                        
                        save_btn = self.get_element(driver, "//button[@guidedhelpid='save_changes_button']", 2)
                        if save_btn:
                            save_btn.click()
                            self.sleep(10)

                # Wait for language change
                for j in range(5):
                    compose_btn = self.get_element(driver, "//div[@role='button' and contains(text(),'Compose')]", 2)
                    if compose_btn:
                        break
                    self.save_log(account['email'], "wait for change lang")
                    self.sleep(10)
            
            return True
        except Exception as e:
            self.save_log(account['email'], f"Language change error: {str(e)}")
            return False

    def open_spam(self, driver, account):
        """Open spam folder"""
        try:
            for i in range(10):
                self.save_log(account['email'], 'https://mail.google.com/mail/u/0/#spam')
                driver.get('https://mail.google.com/mail/u/0/#spam')
                self.sleep(10)
                
                url = driver.current_url
                if "/#spam" in url:
                    self.save_log(account['email'], url)
                    element = self.get_element(driver, "//div[contains(text(),'in Spam more tha') or contains(text(),'Messages that have been in Spam') or contains(text(),'in Spam for more than')]", 10)
                    if element:
                        return True
            return False
        except Exception as e:
            self.save_log(account['email'], str(e))
            return False

    def open_inbox(self, driver, account):
        """Open inbox"""
        try:
            driver.get('https://mail.google.com/mail/u/0/')
            self.sleep(10)
            return True
        except Exception as e:
            self.save_log(account['email'], str(e))
            return False

    def open_promotion(self, driver, account):
        """Open promotion category"""
        try:
            element = self.get_element(driver, "//input[@name='q']", 10)
            if element:
                element.send_keys("category:promotions")
                element.send_keys(Keys.ENTER)
                return True
            return False
        except Exception as e:
            self.save_log(account['email'], str(e))
            return False

    def not_promotion(self, driver, account):
        """Remove promotion label"""
        try:
            element = self.get_element(driver, "(//div[(@aria-label='Labels' or @title='Labels')and @role='button'])[last()]", 2)
            if element:
                element.click()
                self.sleep(2)
                
                promotions_btn = self.get_element(driver, "//div[@role='menuitemcheckbox' and @title='Promotions']", 2)
                if promotions_btn:
                    promotions_btn.click()
                    self.sleep(1)
                    return True
            return False
        except Exception as e:
            self.save_log(account['email'], str(e))
            return False

    def next_page(self, driver, account):
        """Go to next page"""
        try:
            element = self.get_element(driver, "(//div[@role='button' and @aria-label='Older' and not(@aria-disabled)])[last()]", 2)
            if element:
                element.click()
                return True
            return False
        except Exception as e:
            self.save_log(account['email'], str(e))
            return False

    def back_to_inbox(self, driver, account):
        """Go back to inbox"""
        try:
            element = self.get_element(driver, "//div[@role='button' and (@data-tooltip='Back to Inbox' or @title='Back to Inbox' or @title='Back to Search Results' or contains(@aria-label,'Back to Search'))]", 2)
            if element:
                element.click()
        except Exception as e:
            self.save_log(account['email'], str(e))

    def back_to_search(self, driver, account):
        """Go back to search results"""
        try:
            element = self.get_element(driver, "//div[@role='button' and (@data-tooltip='Back to Search Results' or @title='Back to Search Results')]", 2)
            if element:
                element.click()
                return True
            return False
        except Exception as e:
            self.save_log(account['email'], str(e))
            return False

    def scroll_down(self, driver, account):
        """Scroll down in email view"""
        try:
            element = self.get_element(driver, "//div[@role='main']/../../..", 2)
            if element:
                self.save_log(account['email'], "scroll down")
                for i in range(10):
                    driver.execute_script("arguments[0].scrollTop += 150;", element)
                    self.sleep(0.3)
        except Exception as e:
            self.save_log(account['email'], str(e))

    def mark_as_important_by_keyboard(self, driver, account):
        """Mark email as important using keyboard"""
        try:
            self.save_log(account['email'], "Attempting to mark email as important using keyboard")
            
            # Verify email is open
            email_view = self.get_element(driver, "//div[@data-message-id]", 2)
            if not email_view:
                self.save_log(account['email'], "Failed: Not in email view, cannot mark as important")
                return False

            # Press TAB to focus on the first actionable element
            driver.find_element(By.TAG_NAME, 'body').send_keys(Keys.TAB)
            self.sleep(0.5)
            self.save_log(account['email'], "Pressed TAB key")

            # Press ENTER to activate the focused element
            driver.find_element(By.TAG_NAME, 'body').send_keys(Keys.ENTER)
            self.sleep(1)
            self.save_log(account['email'], "Pressed ENTER key")

            # Verify if marked as important
            important_label = self.get_element(driver, "//div[contains(@aria-label, 'Important')]", 1)
            if important_label:
                self.save_log(account['email'], "Successfully marked email as important")
                return True
            else:
                self.save_log(account['email'], "Warning: Could not verify if email was marked as important")
                return False

        except Exception as e:
            self.save_log(account['email'], f"Error in mark_as_important_by_keyboard: {e}")
            return False

    def mark_as_important(self, driver, account):
        """Mark email as important"""
        try:
            element = self.get_element(driver, "//div[@role='main']//div[@aria-label='Not important']", 1)
            if element:
                element.click()
                return True
            return False
        except Exception as e:
            self.save_log(account['email'], str(e))
            return False

    def click_link(self, driver, account):
        """Click links in emails"""
        try:
            element = self.get_element(driver, "//div[@data-message-id]//a[not(contains(@href,'https://support.google.com'))]", 2)
            if element:
                element.click()
                
                # Handle proceed alert
                alert_element = self.get_element(driver, "//div[@role='alertdialog']//button[contains(.,'Proceed')]", 3)
                if alert_element:
                    alert_element.click()
                    self.sleep(10)

                self.sleep(7)
                self.save_log(account['email'], "end click")

                # Close extra tabs
                handles = driver.window_handles
                self.save_log(account['email'], str(len(handles)))
                
                for i in range(1, len(handles)):
                    driver.switch_to.window(handles[i])
                    driver.close()
                    self.sleep(0.5)
                
                driver.switch_to.window(handles[0])
                self.sleep(2)
                return True

        except Exception as e:
            self.save_log(account['email'], str(e))

        return False

    def reply(self, driver, account):
        """Reply to email"""
        try:
            element = self.get_element(driver, "//div[@aria-label='Reply']", 2)
            if element:
                element.click()
                self.sleep(3)
                
                textbox = self.get_element(driver, "//div[@role='textbox']", 2)
                if textbox:
                    textbox.click()
                    self.sleep(2)
                    
                    reply_message = "reply message"
                    
                    # Check for replay.txt file
                    replay_file = self.basedir / 'replay.txt'
                    if replay_file.exists():
                        try:
                            with open(replay_file, 'r') as f:
                                file_content = [line.strip() for line in f.readlines() if line.strip()]
                                if file_content:
                                    reply_message = random.choice(file_content)
                        except Exception as e:
                            self.save_log(account['email'], str(e))
                    
                    textbox.send_keys(reply_message)
                    self.sleep(3)

            send_btn = self.get_element(driver, "//div[@role='button' and contains(text(),'Send')]", 2)
            if send_btn:
                driver.execute_script("arguments[0].scrollIntoView();", send_btn)
                driver.execute_script("window.scrollBy(0, -50);")
                self.sleep(1)
                send_btn.click()
                self.sleep(3)
                
                if self.get_element(driver, "//span[contains(text(),'Message sent')]", 5):
                    self.save_log(account['email'], f"{account['email']} reply ok")
                    return True
                else:
                    self.save_log(account['email'], f"{account['email']} reply NOT OK")

        except Exception as e:
            self.save_log(account['email'], str(e))
        
        return False

    def archive(self, driver, account):
        """Archive email"""
        try:
            element = self.get_element(driver, "((//div[@role='button' and ( @title='Archive' or @aria-label='Archive' ) and not(@aria-disabled)]))", 2)
            if element:
                element.click()
                return True
            return False
        except Exception as e:
            self.save_log(account['email'], str(e))
            return False

    def add_star(self, driver, account):
        """Add star to email"""
        try:
            element = self.get_element(driver, '//div[@role="checkbox" and @aria-label="Not starred"]', 2)
            if element:
                element.click()
                return True
            return False
        except Exception as e:
            self.save_log(account['email'], str(e))
            return False

    def select_all(self, driver, account):
        """Select all messages"""
        try:
            element = self.get_element(driver, "(//div[@aria-label='Select' and @role='button'])[last()]", 5)
            if element:
                element.click()
                return True
            return False
        except Exception as e:
            self.save_log(account['email'], str(e))
            return False

    def messages_not_spam(self, driver, account, search_term=""):
        """Mark messages as not spam"""
        from_email_stats = {}
        try:
            if search_term:
                element = self.get_element(driver, "//input[@name='q']", 10)
                if element:
                    self.save_log(account['email'], search_term)
                    element.send_keys(search_term)
                    element.send_keys(Keys.ENTER)
                    self.sleep(self.random_integer(5000, 7000) / 1000)

            element = self.get_element(driver, "//div[@role='main']//table[@role='grid']//tbody/tr", 20)
            
            if element:
                msg_pos = 0
                nbr_error = 0
                
                while True:
                    try:
                        getelem = self.get_element(driver, "//div[@role='main']//table[@role='grid']//tbody/tr", 1)
                        if getelem:
                            self.save_log(account['email'], f"open message in pos {msg_pos + 1}")
                            msg = self.get_element(driver, f"//div[@role='main']//table[@role='grid']//tbody/tr[{msg_pos + 1}]/td[@role='gridcell']", 2)
                            
                            if msg:
                                # Get sender email
                                email_element = self.get_element(driver, "//div[@role='main']//table[@role='grid']//tbody/tr[1]/td[@role='gridcell']//span[@email]", 1)
                                if email_element:
                                    sender_email = email_element.get_attribute("email")
                                    if sender_email in from_email_stats:
                                        from_email_stats[sender_email] += 1
                                    else:
                                        from_email_stats[sender_email] = 1

                                msg.click()
                                self.sleep(self.random_integer(5000, 7000) / 1000)
                                
                                # Click "Not spam"
                                not_spam_btn = self.get_element(driver, '(//div[@role="button" and (@data-tooltip="Not spam" or @data-tooltip="Move to Inbox" or @aria-label="Not spam" or @aria-label="Report not spam" or @title="Move to Inbox")])[last()]', 3)
                                if not_spam_btn:
                                    not_spam_btn.click()
                                    self.sleep(self.random_integer(4000, 5000) / 1000)
                                
                                nbr_error = 0
                            else:
                                nbr_error += 1
                                if nbr_error > 30:
                                    break
                        else:
                            self.save_log(account['email'], "no message found")
                            break
                    except Exception as e:
                        self.save_log(account['email'], str(e))

        except Exception as e:
            self.save_log(account['email'], str(e))

    def all_not_spam(self, driver, account, search_term=""):
        """Mark all messages as not spam using select all"""
        from_email_stats = {}
        try:
            if search_term:
                element = self.get_element(driver, "//input[@name='q']", 10)
                if element:
                    self.save_log(account['email'], search_term)
                    element.send_keys(search_term)
                    element.send_keys(Keys.ENTER)
                    self.sleep(self.random_integer(5000, 7000) / 1000)

            element = self.get_element(driver, "//div[@role='main']//table[@role='grid']//tbody/tr", 20)
            
            if element:
                nbr_error = 0
                
                while True:
                    try:
                        getelem = self.get_element(driver, "//div[@role='main']//table[@role='grid']//tbody/tr", 1)
                        if getelem:
                            self.save_log(account['email'], "select all")
                            self.select_all(driver, account)
                            self.sleep(self.random_integer(4000, 5000) / 1000)
                            
                            # Click "Not spam" for all selected
                            not_spam_btn = self.get_element(driver,'(//div[@role="button" and (@data-tooltip="Not spam" or @data-tooltip="Move to Inbox" or @aria-label="Not spam" or @aria-label="Report not spam" or @title="Move to Inbox")])[last()]',3)
                            if not_spam_btn:
                                not_spam_btn.click()
                                self.sleep(self.random_integer(10000, 12000) / 1000)
                                nbr_error = 0
                            else:
                                nbr_error += 1
                                if nbr_error > 10:
                                    self.save_log(account['email'], "too many errors")
                                    break
                        else:
                            self.save_log(account['email'], "no message found")
                            break
                    except Exception as e:
                        self.save_log(account['email'], str(e))

        except Exception as e:
            self.save_log(account['email'], str(e))

    def upload_contacts(self, driver, account):
        """Upload contacts from CSV file"""
        try:
            self.save_log(account['email'], "Navigating to Google Contacts...")
            driver.get('https://contacts.google.com/u/0/?hl=en')
            self.sleep(5)

            # Click import button
            import_button = self.get_element(driver, "//div[@role='button']//div[contains(text(),'Import')]", 15)
            if not import_button:
                self.save_log(account['email'], "Could not find the 'Import' button.")
                return False
            
            import_button.click()
            self.sleep(5)

            # Find file input
            file_input = self.get_element(driver, "//div[@role='dialog']//input[@type='file']", 15)
            if not file_input:
                self.save_log(account['email'], "Could not find the file input element in the dialog.")
                return False

            # Upload contacts.csv
            contacts_csv_path = self.basedir / 'contacts.csv'
            if not contacts_csv_path.exists():
                self.save_log(account['email'], f"ERROR: contacts.csv file not found at {contacts_csv_path}")
                return False

            file_input.send_keys(str(contacts_csv_path))
            self.sleep(5)

            # Click final import button
            final_import_button = self.get_element(driver, "//div[@role='dialog']//button/span[contains(text(),'Import')]", 15)
            if not final_import_button:
                self.save_log(account['email'], "Could not find final import button in dialog.")
                return False
            
            final_import_button.click()
            self.save_log(account['email'], "Import process submitted. Waiting for 25 seconds for the page to update...")
            self.sleep(25)

            # Verify import
            today = datetime.now()
            date_string = f"{today.month}/{today.day}"
            import_label_xpath = f"//a[contains(., 'Imported on {date_string}')]"
            self.save_log(account['email'], f"Verifying if import label exists with XPath: {import_label_xpath}")

            label_found = self.get_element(driver, import_label_xpath, 20)
            
            if label_found:
                self.save_log(account['email'], "VERIFICATION SUCCESSFUL: 'Imported on...' label was found.")
                return True
            else:
                self.save_log(account['email'], "VERIFICATION FAILED: 'Imported on...' label was not found.")
                return False

        except Exception as e:
            self.save_log(account['email'], f"Contact upload failed with a critical error: {e}")
            return False

    def open_security(self, driver, account):
        """Open security settings"""
        try:
            self.change_global_language(driver, account)
            driver.get("https://myaccount.google.com/u/0/security?hl=en")
            return True
        except Exception as e:
            self.save_log(account['email'], str(e))
            return False

    def change_global_language(self, driver, account):
        """Change global account language to English"""
        try:
            driver.get('https://myaccount.google.com/personal-info')
            self.sleep(5)
            
            element = self.get_element(driver, "//input[@type='text' and @role='combobox']", 20)
            if element:
                element2 = self.get_element(driver, "//h1[contains(text(),'Personal info')]", 2)
                if not element2:
                    # Change language
                    element_lang = self.get_element(driver, "//a[contains(@href,'language?continue')]", 5)
                    if element_lang:
                        element_lang.click()
                        self.sleep(5)
                        
                        # Select English
                        element_lang = self.get_element(driver, "(//li[@data-id='en']//button)[1]", 5)
                        if element_lang:
                            element_lang.click()
                            self.sleep(3)

                # Wait for language change
                while True:
                    element2 = self.get_element(driver, "//h1[contains(text(),'Personal info')]", 2)
                    if element2:
                        break
                    self.save_log(account['email'], "wait for change lang")
                    self.sleep(10)
            
            return True
        except Exception as e:
            self.save_log(account['email'], str(e))
            return False

    def change_password(self, driver, account):
        """Change account password"""
        try:
            change_success = False
            
            element = self.get_element(driver, "//a[contains(@href,'signinoptions/password')]", 10)
            if element:
                self.sleep(3)
                element.click()
                self.sleep(10)

            # Enter current password
            pass_input = self.get_element(driver, "//input[(@name='Passwd' or @name='password' ) and not(@autocomplete='new-password')]", 10)
            if pass_input:
                pass_input.click()
                pass_input.send_keys(account['password'])
                self.sleep(2)
                pass_input.send_keys(Keys.ENTER)
                self.sleep(10)

            # Handle 2FA if present
            totp_element = self.get_element(driver, "//input[@id='totpPin']", 1)
            if totp_element:
                if '2fa_key' in account and account['2fa_key']:
                    fa_code = pyotp.TOTP(account['2fa_key']).now()
                    self.save_log(account['email'], fa_code)
                    totp_element.click()
                    totp_element.send_keys(fa_code)
                    self.sleep(2)
                    
                    next_button = self.get_element(driver, "//button[@type='button']//span[contains(text(),'Next')]", 5)
                    if next_button:
                        next_button.click()
                    self.sleep(10)

            # Handle recovery email challenge
            recovery_em = self.get_element(driver, "//div[@role='link' and @data-challengetype='12']", 1)
            if recovery_em:
                recovery_em.click()
                self.sleep(5)
                
                recovery_email = self.get_element(driver, "//input[@type='email']", 10)
                if recovery_email:
                    recovery_email.send_keys(account['recovry'])
                    self.sleep(1)
                    recovery_email.send_keys(Keys.ENTER)
                    self.sleep(10)

            # Enter new password
            pass_element = self.get_element(driver, "//input[@name='password' and @autocomplete='new-password']", 10)
            if pass_element:
                new_password = account.get('NewPassword', account['password'])
                self.save_log(account['email'], f"New password : {new_password}")
                
                pass_element.click()
                self.sleep(1)
                pass_element.send_keys(new_password)
                self.sleep(5)
                
                # Confirm new password
                new_pass_element = self.get_element(driver, "//input[@name='confirmation_password']", 2)
                if new_pass_element:
                    new_pass_element.click()
                    self.sleep(1)
                    new_pass_element.send_keys(new_password)
                    self.sleep(5)
                    
                    # Submit form
                    submit_pass_element = self.get_element(driver, "//button[@type='submit']", 2)
                    if submit_pass_element:
                        self.save_log(account['email'], "submit form")
                        submit_pass_element.click()
                        account['password'] = new_password
                        
                        # Log successful password change
                        with open(self.basedir / 'log' / 'changePasswordOk.txt', 'a') as f:
                            f.write(f"{account['email']},{account['password']},{account['proxy']},{account['port']},{account['proxyUsername']},{account['proxyPassword']},{account['recovry']}\n")
                        
                        change_success = True
                        self.sleep(10)

            if not change_success:
                self.save_log(account['email'], f"{account['email']} NOT OK")
                
                # Log failed password change
                with open(self.basedir / 'log' / 'changePasswordNotOK.txt', 'a') as f:
                    f.write(f"{account['email']},{account['password']},{account['proxy']},{account['port']},{account['proxyUsername']},{account['proxyPassword']},{account['recovry']}\n")
                
                # Save screenshot
                screenshot_file = self.basedir / 'log' / 'accountNotOKimage' / f'{account["email"]}.png'
                driver.save_screenshot(str(screenshot_file))

        except Exception as e:
            self.save_log(account['email'], str(e))

        return False

    def change_recovery(self, driver, account):
        """Change recovery email"""
        try:
            change_success = False
            
            driver.get("https://myaccount.google.com/email?hl=en")
            self.sleep(20)
            
            element = self.get_element(driver, "//a[contains(@href,'recovery/email')]", 10)
            if element:
                element.click()
                self.sleep(10)

            # Enter password
            pass_input = self.get_element(driver, "//input[@name='Passwd' or @name='password']", 20)
            if pass_input:
                self.sleep(3)
                pass_input.click()
                pass_input.send_keys(account['password'])
                self.sleep(2)
                pass_input.send_keys(Keys.ENTER)
                self.sleep(7)
                
                # Handle recovery email challenge
                recovery_em = self.get_element(driver, "//div[@role='link' and @data-challengetype='12']", 3)
                if recovery_em:
                    recovery_em.click()
                    self.sleep(5)
                    
                    recovery_email = self.get_element(driver, "//input[@type='email']", 10)
                    if recovery_email:
                        recovery_email.send_keys(account['recovry'])
                        self.sleep(1)
                        recovery_email.send_keys(Keys.ENTER)
                        self.sleep(7)

            self.change_global_language(driver, account)
            driver.get("https://myaccount.google.com/email?hl=en")

            # Update recovery email
            recovery_element = self.get_element(driver, "//input[@type='email' and @value]", 10)
            if recovery_element:
                new_recovery = account.get('NewRecovry', account['recovry'])
                self.save_log(account['email'], f"New recovery : {account['email']} => {new_recovery}")
                
                recovery_element.click()
                recovery_element.send_keys(Keys.CONTROL + "a")
                self.sleep(1)
                recovery_element.send_keys(Keys.BACKSPACE)
                self.sleep(1)
                recovery_element.send_keys(new_recovery)
                self.sleep(5)

                # Submit changes
                submit_element = self.get_element(driver, "//button[@type='submit']", 2)
                if submit_element:
                    submit_element.click()
                    self.sleep(5)
                    
                    # Check for verification input
                    verification_input = self.get_element(driver, "//input[@type='text' and @inputmode='numeric']", 20)
                    if verification_input:
                        account['recovry'] = new_recovery
                        change_success = True

            if change_success:
                self.save_log(account['email'], f"{account['email']} OK")
                
                # Log successful recovery change
                with open(self.basedir / 'log' / 'changeRecovryOk.txt', 'a') as f:
                    f.write(f"{account['email']},{account['password']},{account['proxy']},{account['port']},{account['proxyUsername']},{account['proxyPassword']},{account['recovry']}\n")
            else:
                self.save_log(account['email'], f"{account['email']} NOT OK")
                
                # Log failed recovery change
                with open(self.basedir / 'log' / 'changeRecovryNotOK.txt', 'a') as f:
                    f.write(f"{account['email']},{account['password']},{account['proxy']},{account['port']},{account['proxyUsername']},{account['proxyPassword']},{account['recovry']}\n")

        except Exception as e:
            self.save_log(account['email'], str(e))

        return False

    def activate_2fa(self, driver, account):
        """Activate 2FA"""
        try:
            fa_secret = ""
            
            # Navigate to 2-Step Verification setup page
            driver.get("https://myaccount.google.com/two-step-verification/authenticator")
            self.sleep(10)

            # Enter password if required
            pass_input = self.get_element(driver, "//input[@name='Passwd' or @name='password']", 3)
            if pass_input:
                self.sleep(3)
                pass_input.click()
                pass_input.send_keys(account['password'])
                self.sleep(5)

                next_pass = self.get_element(driver, "//div[@id='passwordNext']", 10)
                if next_pass:
                    next_pass.click()
                    self.sleep(10)

            # Start 2FA setup
            element = self.get_element(driver, "//span[contains(text(), 'Set up')]", 20)
            if element:
                self.sleep(3)
                element.click()
                self.sleep(5)

                element = self.get_element(driver, "//span[@class='mUIrbf-vQzf8d']", 20)
                if element:
                    self.sleep(3)
                    element.click()
                    self.sleep(5)

                # Get 2FA secret
                element = self.get_element(driver, '//*[@wizard-step-uid="Security Center: StrongAuth: Authenticator:manualKey"]/div/ol/li[2]/div/strong', 20)
                if element:
                    self.sleep(3)
                    fa_secret = element.text.replace(' ', '')
                    self.save_log(account['email'], f"2FA Code: {fa_secret}")
                    self.save_log(account['email'], f"{account['email']} , {fa_secret}")
                    
                    fa_code = pyotp.TOTP(fa_secret).now()
                    self.save_log(account['email'], fa_code)
                    account['2fa_key'] = fa_secret

                # Continue setup
                element = self.get_element(driver, "(//span[contains(text(), 'Next')])[2]", 2)
                if element:
                    self.sleep(3)
                    element.click()
                    self.sleep(5)

                # Enter verification code
                element = self.get_element(driver, '//div[@role="dialog"]//input[@type="text"]', 10)
                if element:
                    self.sleep(3)
                    element.send_keys(fa_code)
                    self.sleep(2)

                # Verify code
                element = self.get_element(driver, "(//button//span[contains(text(), 'Verify')])[last()]", 2)
                if element:
                    self.sleep(1)
                    element.click()
                    self.sleep(5)

            # Turn on 2FA
            element = self.get_element(driver, '//*[@aria-label="Turn on"]', 3)
            if element:
                self.sleep(3)
                element.click()
            else:
                element = self.get_element(driver, '//div[@role="button" and @aria-label="Back"]', 1)
                if element:
                    self.sleep(3)
                    element.click()

            # Complete 2FA setup
            element = self.get_element(driver, "//span[text()='Turn on 2-Step Verification']", 10)
            if element:
                self.sleep(3)
                element.click()
                self.sleep(5)

                # Skip additional options
                element = self.get_element(driver, "//button[@aria-label='Skip']", 5)
                if element:
                    element.click()
                    self.sleep(3)
                    
                    element = self.get_element(driver, "//button[@aria-label='Continue anyway']", 5)
                    if element:
                        element.click()
                        self.sleep(3)

                # Finish setup
                element = self.get_element(driver, "//button[@aria-label='Done']", 10)
                if element:
                    element.click()
                    self.sleep(1)
                    
                    # Log successful 2FA activation
                    with open(self.basedir / 'log' / 'AppResult.txt', 'a') as f:
                        f.write(f"{account['email']},{account['password']},{account['proxy']},{account['port']},{account['proxyUsername']},{account['proxyPassword']},{account['recovry']},{account['2fa_key']}\n")

        except Exception as e:
            self.save_log(account['email'], str(e))

    def kill_chrome_process(self, account):
        """Kill Chrome processes for specific account"""
        try:
            email = account['email']
            
            # Find Chrome processes with the account email parameter
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if proc.info['name'] and 'chrome' in proc.info['name'].lower():
                        cmdline = ' '.join(proc.info['cmdline'] or [])
                        if f'parameter-email={email}' in cmdline:
                            self.save_log(email, f"Killing Chrome process: {proc.info['pid']}")
                            proc.terminate()
                            proc.wait(timeout=5)
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    pass
        except Exception as e:
            self.save_log(account.get('email', 'unknown'), f"Error killing Chrome process: {e}")

    def write_cookies_to_file(self, driver, account):
        """Save cookies to file"""
        try:
            cookie_file = self.basedir / 'jsonfiles' / f'{account["email"]}.json'
            cookies = {'cookies': driver.get_cookies()}
            
            with open(cookie_file, 'w') as f:
                json.dump(cookies, f, indent=2)
            
            self.save_log(account['email'], f"Saved cookies to: {cookie_file}")
            return True
            
        except Exception as e:
            self.save_log(account['email'], f"Error writing cookies to file: {e}")
            return False

    def process_account(self, account_info):
        """Process single account with all actions"""
        account = account_info['account']
        actions = account_info['actions']
        search_term = account_info.get('search', '')
        nbr_messages = account_info.get('nbrMessages', 10)
        
        driver = None
        process_start_time = time.time()
        
        try:
            print(f"[{account['email']}] Starting process...")
            
            # Create driver
            driver = self.create_driver(account)
            if not driver:
                print(f"[{account['email']}] Failed to create driver")
                return

            print(f"[{account['email']}] Driver created successfully")
            login_status = False

            # Execute actions
            if '1' in actions:  # Login only
                print(f"[{account['email']}] Executing login action...")
                login_status = self.login_action(driver, account)
                if login_status:
                    print(f"[{account['email']}] Login successful")
                else:
                    print(f"[{account['email']}] Login failed")
                self.save_log(account['email'], "Action '1' selected: Login only. Task complete.")

            if '2' in actions:  # Login and wait
                print(f"[{account['email']}] Login and wait mode...")
                login_status = self.login_action(driver, account)
                if login_status:
                    print(f"[{account['email']}] Login successful, waiting for manual actions...")
                    self.save_log(account['email'], "Waiting for manual actions...")
                    while True:
                        self.sleep(10)
                        try:
                            driver.current_url  # Check if browser is still alive
                            self.write_cookies_to_file(driver, account)
                        except:
                            break

            if '3' in actions:  # Change password
                if not login_status:
                    login_status = self.login_action(driver, account)
                if login_status:
                    print(f"[{account['email']}] Changing password...")
                    if self.open_security(driver, account):
                        self.sleep(5)
                        self.sleep(20)
                        self.change_password(driver, account)
                        self.sleep(5)

            if '4' in actions:  # Change recovery
                if not login_status:
                    login_status = self.login_action(driver, account)
                if login_status:
                    print(f"[{account['email']}] Changing recovery email...")
                    self.change_recovery(driver, account)
                    self.sleep(5)

            if '5' in actions:  # Not spam
                if not login_status:
                    login_status = self.login_action(driver, account)
                if login_status:
                    print(f"[{account['email']}] Processing spam messages...")
                    self.open_spam(driver, account)
                    self.sleep(10)
                    self.messages_not_spam(driver, account, search_term)
                    self.open_inbox(driver, account)

            if '6' in actions:  # Not promotion
                if not login_status:
                    login_status = self.login_action(driver, account)
                if login_status:
                    print(f"[{account['email']}] Processing promotion messages...")
                    self.sleep(10)
                    self.open_promotion(driver, account)
                    self.sleep(10)
                    self.not_promotion(driver, account)

            if any(action in actions for action in ['7', '8', '9', '10', '11']):  # Message actions
                if not login_status:
                    login_status = self.login_action(driver, account)
                if login_status:
                    print(f"[{account['email']}] Processing inbox messages...")
                    self.sleep(10)
                    try:
                        element = self.get_element(driver, "//div[@role='main']//table[@role='grid']//tbody/tr", 20)
                        if element:
                            msg_pos = 0
                            while True:
                                try:
                                    getelems = driver.find_elements(By.XPATH, "//div[@role='main']//table[@role='grid']//tbody/tr")
                                    if not getelems:
                                        self.save_log(account['email'], "no message found")
                                        break
                                    
                                    self.save_log(account['email'], f"nbr messages {len(getelems)}")
                                    
                                    if msg_pos >= len(getelems):
                                        msg_pos = 0
                                        if not self.next_page(driver, account):
                                            self.save_log(account['email'], "last page")
                                            break
                                        else:
                                            self.save_log(account['email'], "next page")
                                            self.sleep(self.random_integer(5000, 7000) / 1000)
                                            continue

                                    if self.random_integer(0, 10) > 4:  # Open message 60%
                                        self.save_log(account['email'], f"open message in pos {msg_pos + 1}")
                                        msg = self.get_element(driver, f"//div[@role='main']//table[@role='grid']//tbody/tr[{msg_pos + 1}]/td[@role='gridcell']", 2)
                                        if not msg:
                                            msg_pos += 1
                                            continue
                                        
                                        msg.click()
                                        self.sleep(self.random_integer(4000, 5000) / 1000)

                                        if '9' in actions:  # Mark as important
                                            self.mark_as_important_by_keyboard(driver, account)
                                            self.sleep(self.random_integer(2000, 3000) / 1000)

                                        if '10' in actions and self.random_integer(0, 10) > 8:  # Click link 20%
                                            self.click_link(driver, account)
                                            self.sleep(self.random_integer(1000, 2000) / 1000)

                                        if '8' in actions and self.random_integer(0, 10) > 7:  # Scroll down 30%
                                            self.scroll_down(driver, account)
                                            self.sleep(self.random_integer(2000, 3000) / 1000)

                                        if '11' in actions and self.random_integer(0, 10) > 8:  # Reply 20%
                                            self.reply(driver, account)
                                            self.sleep(self.random_integer(1000, 2000) / 1000)

                                        self.back_to_inbox(driver, account)
                                        self.sleep(self.random_integer(3000, 4000) / 1000)

                                    msg_pos += 1
                                    
                                except Exception as e:
                                    self.save_log(account['email'], str(e))
                                    msg_pos += 1
                    except Exception as e:
                        self.save_log(account['email'], str(e))

            if '12' in actions:  # Not spam loop
                if not login_status:
                    login_status = self.login_action(driver, account)
                if login_status:
                    print(f"[{account['email']}] Processing spam messages (loop)...")
                    self.open_spam(driver, account)
                    self.sleep(10)
                    self.messages_not_spam(driver, account, search_term)
                    self.sleep(10)

            if '13' in actions:  # Inbox actions
                if not login_status:
                    login_status = self.login_action(driver, account)
                if login_status:
                    print(f"[{account['email']}] Processing inbox messages with archive...")
                    try:
                        if search_term:
                            element = self.get_element(driver, "//input[@name='q']", 10)
                            if element:
                                self.save_log(account['email'], search_term)
                                element.send_keys(f"in:inbox {search_term}")
                                element.send_keys(Keys.ENTER)
                                self.sleep(self.random_integer(5000, 7000) / 1000)

                        element = self.get_element(driver, "//div[@role='main']//table[@role='grid']//tbody/tr", 20)
                        if element:
                            msg_pos = 0
                            nbr_error = 0
                            index = 0
                            
                            while index < nbr_messages:
                                try:
                                    getelem = self.get_element(driver, "//div[@role='main']//table[@role='grid']//tbody/tr", 1)
                                    if getelem:
                                        self.save_log(account['email'], f"open message in pos {msg_pos + 1}")
                                        msg = self.get_element(driver, f"//div[@role='main']//table[@role='grid']//tbody/tr[{msg_pos + 1}]/td[@role='gridcell']", 1)
                                        
                                        if msg:
                                            msg.click()
                                            self.sleep(self.random_integer(4000, 5000) / 1000)
                                            self.click_link(driver, account)
                                            self.sleep(self.random_integer(1000, 2000) / 1000)
                                            self.mark_as_important_by_keyboard(driver, account)
                                            self.sleep(self.random_integer(2000, 3000) / 1000)
                                            self.archive(driver, account)
                                            self.sleep(self.random_integer(4000, 7000) / 1000)
                                            nbr_error = 0
                                        else:
                                            nbr_error += 1
                                            if nbr_error > 30:
                                                break

                                        self.back_to_inbox(driver, account)
                                        self.sleep(self.random_integer(2000, 3000) / 1000)
                                    else:
                                        self.save_log(account['email'], "no message found")
                                        break
                                except Exception as e:
                                    self.save_log(account['email'], str(e))
                                
                                index += 1
                    except Exception as e:
                        self.save_log(account['email'], str(e))

            if '14' in actions:  # Upload contacts
                if not login_status:
                    login_status = self.login_action(driver, account)
                if login_status:
                    print(f"[{account['email']}] Uploading contacts...")
                    success = self.upload_contacts(driver, account)
                    account_details = f"{account['email']},{account['password']},{account['proxy']},{account['port']},{account['proxyUsername']},{account['proxyPassword']},{account['recovry']}"
                    
                    if success:
                        self.save_log(account['email'], "Contact import was successful. Logging to success file.")
                        with open(self.basedir / 'log' / 'contactImportSuccess.txt', 'a') as f:
                            f.write(account_details + "\n")
                    else:
                        self.save_log(account['email'], "Contact import failed. Logging to failure file.")
                        with open(self.basedir / 'log' / 'contactImportFailed.txt', 'a') as f:
                            f.write(account_details + "\n")

            if '15' in actions:  # Advanced inbox actions
                if not login_status:
                    login_status = self.login_action(driver, account)
                if login_status:
                    print(f"[{account['email']}] Processing advanced inbox actions...")
                    self.sleep(3)
                    try:
                        if search_term:
                            element = self.get_element(driver, "//input[@name='q']", 10)
                            if element:
                                self.save_log(account['email'], search_term)
                                element.send_keys(f"in:inbox {search_term}")
                                element.send_keys(Keys.ENTER)
                                self.sleep(self.random_integer(5000, 7000) / 1000)

                        element = self.get_element(driver, "//div[@role='main']//table[@role='grid']//tbody/tr", 20)
                        nbr_messages = self.random_integer(7, 10)
                        
                        if element:
                            msg_pos = 0
                            index = 0
                            
                            while index < nbr_messages:
                                try:
                                    getelems = driver.find_elements(By.XPATH, "//div[@role='main']//table[@role='grid']//tbody/tr")
                                    self.save_log(account['email'], f"nbr messages {len(getelems)}")
                                    
                                    if getelems:
                                        if msg_pos >= len(getelems):
                                            msg_pos = 0
                                            if not self.next_page(driver, account):
                                                self.save_log(account['email'], "last page")
                                                break
                                            else:
                                                self.save_log(account['email'], "next page")
                                                self.sleep(self.random_integer(5000, 7000) / 1000)
                                                continue

                                        if self.random_integer(0, 10) > 3:
                                            self.save_log(account['email'], f"open message in pos {msg_pos + 1}")
                                            msg = self.get_element(driver, f"//div[@role='main']//table[@role='grid']//tbody/tr[{msg_pos + 1}]/td[@role='gridcell']", 2)
                                            if not msg:
                                                msg_pos += 1
                                                continue
                                            
                                            msg.click()
                                            self.sleep(self.random_integer(4000, 5000) / 1000)
                                            
                                            self.mark_as_important_by_keyboard(driver, account)
                                            self.sleep(self.random_integer(2000, 3000) / 1000)
                                            
                                            if self.random_integer(0, 10) > 5:
                                                self.click_link(driver, account)
                                                self.sleep(self.random_integer(1000, 4000) / 1000)
                                            
                                            if self.random_integer(0, 10) > 9:
                                                self.add_star(driver, account)
                                                self.sleep(self.random_integer(1000, 4000) / 1000)
                                            
                                            if self.random_integer(0, 10) > 9:
                                                self.reply(driver, account)
                                                self.sleep(self.random_integer(1000, 4000) / 1000)
                                            
                                            if self.random_integer(0, 10) > 8:
                                                self.archive(driver, account)
                                                self.sleep(self.random_integer(1000, 4000) / 1000)
                                            
                                            self.back_to_inbox(driver, account)
                                            self.sleep(self.random_integer(1000, 4000) / 1000)

                                        msg_pos += 1
                                    else:
                                        self.save_log(account['email'], "no message found")
                                        break
                                except Exception as e:
                                    self.save_log(account['email'], str(e))
                                
                                index += 1
                    except Exception as e:
                        self.save_log(account['email'], str(e))

            if '16' in actions:  # Spam + inbox actions
                if not login_status:
                    login_status = self.login_action(driver, account)
                if login_status:
                    print(f"[{account['email']}] Processing spam + inbox actions...")
                    self.sleep(3)
                    try:
                        self.open_spam(driver, account)
                        self.sleep(10)
                        self.all_not_spam(driver, account, search_term)
                        self.open_inbox(driver, account)
                        
                        if search_term:
                            element = self.get_element(driver, "//input[@name='q']", 10)
                            if element:
                                self.save_log(account['email'], search_term)
                                element.send_keys(f"in:inbox {search_term}")
                                element.send_keys(Keys.ENTER)
                                self.sleep(self.random_integer(5000, 7000) / 1000)

                        element = self.get_element(driver, "//div[@role='main']//table[@role='grid']//tbody/tr", 20)
                        nbr_messages = self.random_integer(7, 10)
                        
                        if element:
                            msg_pos = 0
                            nbr_error = 0
                            index = 0
                            
                            while index < nbr_messages:
                                try:
                                    getelem = self.get_element(driver, "//div[@role='main']//table[@role='grid']//tbody/tr", 1)
                                    if getelem:
                                        self.save_log(account['email'], f"open message in pos {msg_pos + 1}")
                                        msg = self.get_element(driver, f"//div[@role='main']//table[@role='grid']//tbody/tr[{msg_pos + 1}]/td[@role='gridcell']", 1)
                                        
                                        if msg:
                                            msg.click()
                                            self.sleep(self.random_integer(4000, 5000) / 1000)
                                            self.click_link(driver, account)
                                            self.sleep(self.random_integer(1000, 2000) / 1000)
                                            self.mark_as_important_by_keyboard(driver, account)
                                            self.sleep(self.random_integer(2000, 3000) / 1000)
                                            self.archive(driver, account)
                                            self.sleep(self.random_integer(4000, 7000) / 1000)
                                            nbr_error = 0
                                        else:
                                            nbr_error += 1
                                            if nbr_error > 30:
                                                break

                                        self.back_to_inbox(driver, account)
                                        self.sleep(self.random_integer(2000, 3000) / 1000)
                                    else:
                                        self.save_log(account['email'], "no message found")
                                        break
                                except Exception as e:
                                    self.save_log(account['email'], str(e))
                                
                                index += 1
                    except Exception as e:
                        self.save_log(account['email'], str(e))

            if '17' in actions:  # Activate 2FA
                if not login_status:
                    login_status = self.login_action(driver, account)
                if login_status:
                    print(f"[{account['email']}] Activating 2FA...")
                    self.activate_2fa(driver, account)
                    self.sleep(5)

            if '18' in actions:  # Change recovery and password
                if not login_status:
                    login_status = self.login_action(driver, account)
                if login_status:
                    print(f"[{account['email']}] Changing recovery and password...")
                    self.change_recovery(driver, account)
                    self.sleep(5)
                    if self.open_security(driver, account):
                        self.sleep(20)
                        self.change_password(driver, account)
                        self.sleep(5)

            if '19' in actions:  # Advanced spam + inbox
                if not login_status:
                    login_status = self.login_action(driver, account)
                if login_status:
                    print(f"[{account['email']}] Processing advanced spam + inbox...")
                    self.sleep(3)
                    try:
                        self.open_spam(driver, account)
                        self.sleep(10)
                        self.all_not_spam(driver, account, search_term)
                        self.open_inbox(driver, account)

                        if search_term:
                            element = self.get_element(driver, "//input[@name='q']", 10)
                            if element:
                                self.save_log(account['email'], search_term)
                                element.send_keys(f"in:inbox {search_term}")
                                element.send_keys(Keys.ENTER)
                                self.sleep(self.random_integer(5000, 7000) / 1000)

                        element = self.get_element(driver, "//div[@role='main']//table[@role='grid']//tbody/tr", 20)
                        if element:
                            msg_pos = 0
                            nbr_error = 0
                            index = 0
                            list_messages = []
                            
                            while index < nbr_messages:
                                try:
                                    getelem = self.get_element(driver, "//div[@role='main']//table[@role='grid']//tbody/tr", 1)
                                    if getelem:
                                        if msg_pos == 0:
                                            messages = self.get_all_elements(driver, "//div[@role='main']//table[@role='grid']//tbody/tr", 1)
                                            list_messages = list(range(len(messages)))
                                            list_messages = self.shuffle_list(list_messages)

                                        self.save_log(account['email'], f"open message in pos {list_messages[msg_pos]}")
                                        msg = self.get_element(driver, f"//div[@role='main']//table[@role='grid']//tbody/tr[{list_messages[msg_pos]}]/td[@role='gridcell']", 1)
                                        
                                        if msg:
                                            msg.click()
                                            self.sleep(self.random_integer(4000, 5000) / 1000)
                                            self.mark_as_important(driver, account)
                                            self.sleep(self.random_integer(2000, 3000) / 1000)
                                            self.add_star(driver, account)
                                            self.sleep(self.random_integer(1000, 2000) / 1000)
                                            self.click_link(driver, account)
                                            self.sleep(self.random_integer(1000, 2000) / 1000)
                                            nbr_error = 0
                                        else:
                                            nbr_error += 1
                                            if nbr_error > 30:
                                                break

                                        self.back_to_search(driver, account)
                                        self.sleep(self.random_integer(2000, 3000) / 1000)

                                        msg_pos += 1
                                        if msg_pos >= len(list_messages):
                                            msg_pos = 0
                                            if not self.next_page(driver, account):
                                                self.save_log(account['email'], "last page")
                                                break
                                            else:
                                                self.sleep(self.random_integer(5000, 7000) / 1000)
                                    else:
                                        self.save_log(account['email'], "no message found")
                                        break
                                except Exception as e:
                                    self.save_log(account['email'], str(e))
                                
                                index += 1
                    except Exception as e:
                        self.save_log(account['email'], str(e))

            if '20' in actions:  # Not spam (select all)
                if not login_status:
                    login_status = self.login_action(driver, account)
                if login_status:
                    print(f"[{account['email']}] Processing spam messages (select all)...")
                    self.save_log(account['email'], "Action 20: Starting 'notspam (select all)' process.")
                    self.sleep(3)
                    
                    if self.open_spam(driver, account):
                        self.sleep(5)
                        self.all_not_spam(driver, account, search_term)
                        self.save_log(account['email'], "Finished 'notspam (select all)' process. Returning to inbox.")
                        self.open_inbox(driver, account)
                    else:
                        self.save_log(account['email'], "Could not open spam folder for Action 20.")

            # Save cookies before closing
            self.write_cookies_to_file(driver, account)

            elapsed_time = time.time() - process_start_time
            print(f"[{account['email']}] Process completed in {elapsed_time:.2f} seconds")
            self.save_log(account['email'], f"Process completed in {elapsed_time:.2f} seconds")

        except Exception as e:
            elapsed_time = time.time() - process_start_time
            error_msg = f"Process error after {elapsed_time:.2f} seconds: {str(e)}"
            print(f"[{account['email']}] {error_msg}")
            self.save_log(account.get('email', 'unknown'), error_msg)

        finally:
            if driver:
                try:
                    driver.quit()
                    print(f"[{account['email']}] Driver closed")
                except:
                    print(f"[{account['email']}] Error closing driver")
                
                # Kill any remaining processes
                self.kill_chrome_process(account)


class GmailManager:
    def __init__(self):
        self.basedir = Path(__file__).parent
        self.automation = GmailAutomation(self.basedir)

    def load_config(self):
        """Load configuration from files"""
        config_file = self.basedir / 'config.json'
        accounts_file = self.basedir / 'Accounts.txt'
        
        if not config_file.exists():
            print(f"FATAL ERROR: config.json not found at {config_file}")
            input('Press ENTER to exit.')
            sys.exit(1)
            
        if not accounts_file.exists():
            print(f"FATAL ERROR: Accounts.txt not found at {accounts_file}")
            input('Press ENTER to exit.')
            sys.exit(1)

        with open(config_file, 'r') as f:
            parameters = json.load(f)

        accounts = []
        with open(accounts_file, 'r') as f:
            for line in f:
                line = line.strip().replace('\r', '').replace('\n', '')
                if not line:
                    continue
                
                parts = line.split(',')
                
                if len(parts) >= 7:
                    account = {
                        "email": parts[0],
                        "password": parts[1],
                        "proxy": parts[2],
                        "port": parts[3],
                        "proxyUsername": parts[4],
                        "proxyPassword": parts[5],
                        "recovry": parts[6]
                    }
                    
                    if len(parts) > 7:
                        account["2fa_key"] = parts[7]
                    if len(parts) > 8:
                        account["NewRecovry"] = parts[8]
                    if len(parts) > 9:
                        account["NewPassword"] = parts[9]
                    
                    accounts.append(account)

        return parameters, accounts

    def show_menu(self):
        """Show action menu"""
        print(
            ' =========== Gmail Automation ===========' + '\n' +
            ' 1    : login only' + '\n' +
            ' 2    : login and wait for actions' + '\n' +
            ' 3    : change password' + '\n' +
            ' 4    : change recovery email' + '\n' +
            ' 5    : mark spam as not spam' + '\n' +
            ' 6    : not promotion' + '\n' +
            ' 7    : open message' + '\n' +
            ' 8    : open message + scroll down' + '\n' +
            ' 9    : open message + mark as important' + '\n' +
            ' 10   : open message + click link' + '\n' +
            ' 11   : open message + reply' + '\n' +
            ' 12   : not spam {loop}' + '\n' +
            ' 13   : inbox > open message + click + mark as important + archive {loop}' + '\n' +
            ' 14   : upload contacts' + '\n' +
            ' 15   : inbox > open message + click + mark as important + add star + reply + archive {loop %}' + '\n' +
            ' 16   : spam search + not spam > inbox search + open message + click + mark as important + archive {loop}' + '\n' +
            ' 17   : activate 2fa' + '\n' +
            ' 18   : change recovery and password' + '\n' +
            ' 19   : spam search + not spam > inbox search + open message + mark as important + add star + click (page by page)' + '\n' +
            ' 20   : notspam (select all)' + '\n'
        )

    def run(self):
        """Main execution function"""
        try:
            # Load configuration
            parameters, accounts = self.load_config()

            # Show menu and get actions
            self.show_menu()
            actions_input = input('Choose number, then press ENTER: ')
            actions = actions_input.split()

            if not actions:
                input('Error: No actions selected. Press ENTER to exit.')
                return

            # Confirm destructive actions
            if any(action in actions for action in ['3', '4', '17', '18']):
                confirm = input('Are you sure (yes): ')
                if confirm != "yes":
                    input('Error: Press ENTER to exit.')
                    return

            # Prepare account info
            account_infos = []
            for account in accounts:
                account_info = {
                    "account": account,
                    "actions": actions,
                    "nbrMessages": parameters.get("nbrMessages", 10),
                    "search": parameters.get("search", ""),
                    "basedir": str(self.basedir)
                }
                account_infos.append(account_info)

            # Execute with parallel processing
            max_workers = parameters.get("nbrAccounts", 3)
            print(f"Starting {max_workers} parallel workers for {len(account_infos)} accounts...")
            
            for r in range(parameters.get("duplication", 1)):
                print(f"=== Duplication Round {r + 1} ===")
                
                # Process accounts in batches based on max_workers
                for i in range(0, len(account_infos), max_workers):
                    batch = account_infos[i:i + max_workers]
                    
                    print(f"Processing batch {i//max_workers + 1}: {len(batch)} accounts")
                    
                    with ThreadPoolExecutor(max_workers=len(batch)) as executor:
                        # Submit all tasks in this batch
                        futures = []
                        for account_info in batch:
                            print(f"Starting account: {account_info['account']['email']}")
                            future = executor.submit(self.automation.process_account, account_info)
                            futures.append((future, account_info['account']['email']))
                            time.sleep(5)  # Delay between account starts
                        
                        # Wait for completion of this batch
                        for future, email in futures:
                            try:
                                future.result()
                                print(f"✓ Completed account: {email}")
                            except Exception as e:
                                print(f"✗ Account {email} failed: {e}")
                    
                    print(f"Batch {i//max_workers + 1} completed.")
                    if i + max_workers < len(account_infos):  # Not the last batch
                        time.sleep(10)  # Wait between batches
                
                # Break if not looping actions
                if not any(action in actions for action in ['12', '13', '15', '16', '19']):
                    break
                
                # Wait before next iteration
                if r < parameters.get("duplication", 1) - 1:  # Not the last round
                    print("Waiting 60 seconds before next duplication round...")
                    time.sleep(60)

            print(" All processing completed!")

        except Exception as e:
            print(f"An unexpected error occurred: {e}")
            input("Press ENTER to exit.")


def main():
    """Main entry point"""
    try:
        manager = GmailManager()
        manager.run()
    except KeyboardInterrupt:
        print("\n  Operation cancelled by user.")
    except Exception as e:
        print(f"Fatal error: {e}")
        input("Press ENTER to exit.")


if __name__ == "__main__":
    main()