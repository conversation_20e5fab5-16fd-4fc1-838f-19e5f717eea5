#!/usr/bin/env python3
"""
Local proxy tunnel to handle authentication automatically
This creates a local proxy that forwards requests to the authenticated proxy
"""

import socket
import threading
import base64
import time
import logging
from urllib.parse import urlparse

class ProxyTunnel:
    def __init__(self, local_port, remote_host, remote_port, username, password):
        self.local_port = local_port
        self.remote_host = remote_host
        self.remote_port = remote_port
        self.username = username
        self.password = password
        self.running = False
        self.server_socket = None
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
    def start(self):
        """Start the proxy tunnel"""
        try:
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.bind(('127.0.0.1', self.local_port))
            self.server_socket.listen(5)
            
            self.running = True
            self.logger.info(f"Proxy tunnel started on 127.0.0.1:{self.local_port}")
            self.logger.info(f"Forwarding to {self.remote_host}:{self.remote_port}")
            
            while self.running:
                try:
                    client_socket, addr = self.server_socket.accept()
                    self.logger.info(f"Connection from {addr}")
                    
                    # Handle connection in separate thread
                    thread = threading.Thread(
                        target=self.handle_client,
                        args=(client_socket,)
                    )
                    thread.daemon = True
                    thread.start()
                    
                except Exception as e:
                    if self.running:
                        self.logger.error(f"Error accepting connection: {e}")
                        
        except Exception as e:
            self.logger.error(f"Error starting proxy tunnel: {e}")
            
    def stop(self):
        """Stop the proxy tunnel"""
        self.running = False
        if self.server_socket:
            self.server_socket.close()
        self.logger.info("Proxy tunnel stopped")
        
    def handle_client(self, client_socket):
        """Handle client connection"""
        try:
            # Read the request
            request = client_socket.recv(4096).decode('utf-8')
            if not request:
                return
                
            self.logger.info(f"Request: {request.split()[0] if request.split() else 'Unknown'}")
            
            # Parse the request
            lines = request.split('\n')
            first_line = lines[0]
            method, url, version = first_line.split()
            
            if method == 'CONNECT':
                # Handle HTTPS CONNECT
                self.handle_connect(client_socket, url)
            else:
                # Handle HTTP request
                self.handle_http(client_socket, request)
                
        except Exception as e:
            self.logger.error(f"Error handling client: {e}")
        finally:
            client_socket.close()
            
    def handle_connect(self, client_socket, url):
        """Handle HTTPS CONNECT method"""
        try:
            # Connect to remote proxy
            remote_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            remote_socket.connect((self.remote_host, self.remote_port))
            
            # Send CONNECT request with authentication
            auth_string = base64.b64encode(f"{self.username}:{self.password}".encode()).decode()
            connect_request = f"CONNECT {url} HTTP/1.1\r\n"
            connect_request += f"Proxy-Authorization: Basic {auth_string}\r\n"
            connect_request += "\r\n"
            
            remote_socket.send(connect_request.encode())
            
            # Read response
            response = remote_socket.recv(4096)
            
            # Send response to client
            client_socket.send(response)
            
            if b"200 Connection established" in response:
                # Start tunneling
                self.tunnel_data(client_socket, remote_socket)
            else:
                self.logger.error(f"CONNECT failed: {response.decode()}")
                
        except Exception as e:
            self.logger.error(f"Error in CONNECT: {e}")
            
    def handle_http(self, client_socket, request):
        """Handle HTTP request"""
        try:
            # Connect to remote proxy
            remote_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            remote_socket.connect((self.remote_host, self.remote_port))
            
            # Add authentication header
            auth_string = base64.b64encode(f"{self.username}:{self.password}".encode()).decode()
            
            # Modify request to add auth header
            lines = request.split('\n')
            auth_header = f"Proxy-Authorization: Basic {auth_string}\r\n"
            
            # Insert auth header after first line
            modified_request = lines[0] + '\n' + auth_header
            for line in lines[1:]:
                modified_request += line + '\n'
            
            # Send modified request
            remote_socket.send(modified_request.encode())
            
            # Forward response
            while True:
                data = remote_socket.recv(4096)
                if not data:
                    break
                client_socket.send(data)
                
        except Exception as e:
            self.logger.error(f"Error in HTTP: {e}")
            
    def tunnel_data(self, client_socket, remote_socket):
        """Tunnel data between client and remote"""
        def forward(source, destination):
            try:
                while True:
                    data = source.recv(4096)
                    if not data:
                        break
                    destination.send(data)
            except:
                pass
            finally:
                source.close()
                destination.close()
                
        # Start forwarding in both directions
        thread1 = threading.Thread(target=forward, args=(client_socket, remote_socket))
        thread2 = threading.Thread(target=forward, args=(remote_socket, client_socket))
        
        thread1.daemon = True
        thread2.daemon = True
        
        thread1.start()
        thread2.start()
        
        thread1.join()
        thread2.join()

def create_proxy_tunnel(proxy_host, proxy_port, username, password, local_port=None):
    """Create and start a proxy tunnel"""
    if local_port is None:
        # Find available port
        import random
        local_port = random.randint(8000, 9000)
        
    tunnel = ProxyTunnel(local_port, proxy_host, proxy_port, username, password)
    
    # Start in separate thread
    thread = threading.Thread(target=tunnel.start)
    thread.daemon = True
    thread.start()
    
    # Wait a bit for tunnel to start
    time.sleep(2)
    
    return tunnel, local_port

if __name__ == "__main__":
    # Test the proxy tunnel
    tunnel = ProxyTunnel(8888, "*************", 3128, "admin123", "Hello123")
    try:
        tunnel.start()
    except KeyboardInterrupt:
        tunnel.stop()
