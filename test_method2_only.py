#!/usr/bin/env python3
"""
Test Method 2 (Authenticated Proxy String) - Your Working Method
"""

import sys
import time
import importlib.util
from pathlib import Path

def import_main_module():
    spec = importlib.util.spec_from_file_location("main_module", "2.py")
    main_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(main_module)
    return main_module.GmailAutomation

def test_method2():
    """Test Method 2 specifically"""
    
    print("🎯 METHOD 2 TEST - Authenticated Proxy String")
    print("=" * 60)
    
    # Load account
    basedir = Path(__file__).parent
    accounts_file = basedir / 'Accounts.txt'
    
    if not accounts_file.exists():
        print("❌ ERROR: Accounts.txt not found!")
        return False
    
    with open(accounts_file, 'r') as f:
        line = f.readline().strip()
        if not line:
            print("❌ ERROR: No accounts found!")
            return False
    
    parts = line.split(',')
    if len(parts) < 7:
        print("❌ ERROR: Invalid account format!")
        return False
    
    account = {
        "email": parts[0],
        "password": parts[1],
        "proxy": parts[2],
        "port": parts[3],
        "proxyUsername": parts[4],
        "proxyPassword": parts[5],
        "recovry": parts[6]
    }
    
    print(f"📧 Account: {account['email']}")
    print(f"🌐 Proxy: {account['proxy']}:{account['port']}")
    print(f"🔑 Auth: {account['proxyUsername']}:{'*' * len(account['proxyPassword'])}")
    
    # Show the authenticated proxy string that will be used
    proxy_ip = account['proxy'].replace(',', '.')
    auth_proxy_string = f"{account['proxyUsername']}:{account['proxyPassword']}@{proxy_ip}:{account['port']}"
    masked_proxy = f"{account['proxyUsername']}:{'*' * len(account['proxyPassword'])}@{proxy_ip}:{account['port']}"
    
    print(f"🔗 Authenticated Proxy URL: {masked_proxy}")
    print()
    
    # Initialize automation
    GmailAutomation = import_main_module()
    automation = GmailAutomation(basedir)
    
    profile_dir = basedir / 'profiles' / account['email']
    profile_dir.mkdir(parents=True, exist_ok=True)
    
    driver = None
    try:
        print("🔄 Creating driver with Method 2...")
        driver = automation._create_driver_method2_auth_proxy(account, profile_dir)
        
        if not driver:
            print("❌ Driver creation failed")
            return False
        
        print("✅ Driver created successfully")
        print("🔍 Testing proxy authentication...")
        
        # Test 1: IP check
        print("\n📍 TEST 1: Checking IP through proxy")
        driver.get('http://httpbin.org/ip')
        print("⏳ Waiting 15 seconds to check for manual auth prompts...")
        print("   (If you see username/password dialog, Method 2 failed)")
        
        time.sleep(15)
        
        current_url = driver.current_url
        page_source = driver.page_source.lower()
        
        print(f"📍 Current URL: {current_url}")
        
        if 'httpbin.org' in current_url and ('origin' in page_source or 'ip' in page_source):
            print("✅ TEST 1 PASSED: Got IP response without manual auth!")
            
            # Try to extract and show the IP
            try:
                import json
                page_text = driver.page_source
                if '{' in page_text and '}' in page_text:
                    start = page_text.find('{')
                    end = page_text.rfind('}') + 1
                    json_data = json.loads(page_text[start:end])
                    proxy_ip_result = json_data.get('origin', 'Unknown')
                    print(f"🌐 Your IP through proxy: {proxy_ip_result}")
                    
                    # Check if it matches your proxy IP
                    expected_proxy_ip = account['proxy'].replace(',', '.')
                    if expected_proxy_ip in proxy_ip_result:
                        print(f"✅ Confirmed: Using proxy IP {expected_proxy_ip}")
                    else:
                        print(f"⚠️ Note: Proxy IP might be different than expected")
            except:
                print("📄 Got response but couldn't parse IP")
        else:
            print("❌ TEST 1 FAILED: No proper response")
            return False
        
        # Test 2: Gmail navigation
        print("\n📍 TEST 2: Gmail navigation")
        driver.get('https://accounts.google.com')
        time.sleep(10)
        
        gmail_url = driver.current_url
        print(f"📍 Gmail URL: {gmail_url}")
        
        if 'accounts.google.com' in gmail_url:
            print("✅ TEST 2 PASSED: Gmail navigation successful")
        else:
            print(f"⚠️ TEST 2 WARNING: Unexpected URL")
        
        print("\n🎉 METHOD 2 TESTING COMPLETE!")
        print("✅ Method 2 (Authenticated Proxy String) is working perfectly!")
        print("🚀 Your script will now use this method automatically")
        
        return True
        
    except Exception as e:
        print(f"❌ Method 2 test failed: {e}")
        return False
        
    finally:
        if driver:
            try:
                driver.quit()
                print("🔒 Driver closed")
            except:
                pass

def main():
    print("🧪 Testing Your Working Method (Method 2)")
    print("This confirms Method 2 works and explains how it functions")
    print()
    
    success = test_method2()
    
    if success:
        print("\n" + "=" * 60)
        print("🎯 HOW METHOD 2 WORKS:")
        print("=" * 60)
        print("1. 🔗 Embeds username:password in proxy URL")
        print("2. 🌐 Chrome uses authenticated proxy automatically") 
        print("3. ✅ No manual authentication prompts")
        print("4. 🚀 Perfect for bulk automation")
        print()
        print("🎉 YOUR SCRIPT IS READY FOR BULK OPERATIONS!")
        print("   Run: python 2.py")
        print("   Method 2 will be used automatically")
        
    else:
        print("\n❌ Method 2 test failed")
        print("Check your proxy credentials and server accessibility")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
