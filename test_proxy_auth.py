#!/usr/bin/env python3
"""
Test script to verify proxy authentication fixes
"""

import sys
import time
from pathlib import Path
from 2 import GmailAutomation

def test_proxy_authentication():
    """Test proxy authentication with a single account"""
    
    print("=" * 60)
    print("PROXY AUTHENTICATION TEST")
    print("=" * 60)
    
    # Initialize automation
    basedir = Path(__file__).parent
    automation = GmailAutomation(basedir)
    
    # Load test account from Accounts.txt
    accounts_file = basedir / 'Accounts.txt'
    if not accounts_file.exists():
        print("ERROR: Accounts.txt not found!")
        return False
    
    # Parse first account
    with open(accounts_file, 'r') as f:
        line = f.readline().strip()
        if not line:
            print("ERROR: No accounts found in Accounts.txt!")
            return False
    
    parts = line.split(',')
    if len(parts) < 7:
        print("ERROR: Invalid account format in Accounts.txt!")
        print("Expected format: email,password,proxy,port,proxyUsername,proxyPassword,recovery")
        return False
    
    account = {
        "email": parts[0],
        "password": parts[1],
        "proxy": parts[2],
        "port": parts[3],
        "proxyUsername": parts[4],
        "proxyPassword": parts[5],
        "recovry": parts[6]
    }
    
    print(f"Testing account: {account['email']}")
    print(f"Proxy: {account['proxy']}:{account['port']}")
    print(f"Proxy Auth: {account['proxyUsername']}:{'*' * len(account['proxyPassword'])}")
    print()
    
    # Test 1: Create proxy auth extension
    print("Test 1: Creating proxy auth extension...")
    extension_path = automation.create_proxy_auth_extension(account)
    if extension_path:
        print(f"✓ Extension created at: {extension_path}")
        
        # Verify extension files exist
        ext_dir = Path(extension_path)
        required_files = ['manifest.json', 'background.js', 'content.js']
        for file_name in required_files:
            if (ext_dir / file_name).exists():
                print(f"  ✓ {file_name} exists")
            else:
                print(f"  ✗ {file_name} missing")
                return False
        
        # Check if credentials were injected
        with open(ext_dir / 'background.js', 'r') as f:
            bg_content = f.read()
            if account['proxyUsername'] in bg_content and account['proxyPassword'] in bg_content:
                print("  ✓ Credentials properly injected into extension")
            else:
                print("  ✗ Credentials not found in extension")
                return False
    else:
        print("✗ Failed to create extension")
        return False
    
    print()
    
    # Test 2: Create driver with proxy
    print("Test 2: Creating driver with proxy configuration...")
    driver = automation.create_driver(account)
    if driver:
        print("✓ Driver created successfully")
        
        try:
            # Test 3: Verify proxy connection
            print("\nTest 3: Verifying proxy connection...")
            if automation.verify_proxy_connection(driver, account):
                print("✓ Proxy connection verified")
            else:
                print("⚠ Proxy connection verification failed (may still work)")
            
            # Test 4: Test navigation with proxy
            print("\nTest 4: Testing navigation with proxy...")
            try:
                driver.get('https://httpbin.org/ip')
                time.sleep(5)
                
                page_source = driver.page_source
                if 'origin' in page_source.lower():
                    print("✓ Successfully navigated with proxy")
                    print("✓ No manual authentication prompts appeared")
                else:
                    print("⚠ Navigation completed but response unclear")
                
            except Exception as e:
                print(f"✗ Navigation failed: {e}")
                # Try to handle auth failure
                if automation.handle_proxy_auth_failure(driver, account):
                    print("✓ Handled proxy auth failure")
                else:
                    print("✗ Could not handle proxy auth failure")
            
            # Test 5: Test Gmail login
            print("\nTest 5: Testing Gmail login with proxy...")
            try:
                driver.get('https://accounts.google.com')
                time.sleep(5)
                
                # Check for auth dialogs
                automation.handle_proxy_auth_failure(driver, account)
                
                current_url = driver.current_url
                if 'accounts.google.com' in current_url:
                    print("✓ Successfully reached Gmail login page")
                    print("✓ No proxy authentication prompts detected")
                else:
                    print(f"⚠ Unexpected URL: {current_url}")
                
            except Exception as e:
                print(f"✗ Gmail navigation failed: {e}")
        
        finally:
            print("\nClosing driver...")
            try:
                driver.quit()
                print("✓ Driver closed successfully")
            except:
                print("⚠ Error closing driver")
    else:
        print("✗ Failed to create driver")
        return False
    
    print()
    print("=" * 60)
    print("PROXY AUTHENTICATION TEST COMPLETED")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    success = test_proxy_authentication()
    if success:
        print("\n🎉 All tests passed! Proxy authentication should work automatically.")
        print("\nNext steps:")
        print("1. Run your main script (python 2.py)")
        print("2. Select action '1' for login test")
        print("3. Monitor logs for any proxy authentication issues")
    else:
        print("\n❌ Some tests failed. Please check the errors above.")
        print("\nTroubleshooting:")
        print("1. Verify your proxy credentials in Accounts.txt")
        print("2. Check if your proxy server is accessible")
        print("3. Ensure your proxy supports HTTP CONNECT method")
    
    sys.exit(0 if success else 1)
