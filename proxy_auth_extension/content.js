// Content script to handle proxy auth popups and dialogs
(function() {
    'use strict';

    console.log('Proxy auth content script initializing...');

    // Function to handle proxy authentication dialogs
    function handleProxyAuthDialogs() {
        // Look for proxy authentication dialogs
        const authDialogs = document.querySelectorAll([
            'dialog[open]',
            '.auth-dialog',
            '[role="dialog"]',
            '.modal',
            '.popup',
            'div[class*="auth"]',
            'div[class*="proxy"]'
        ].join(','));

        authDialogs.forEach(dialog => {
            const dialogText = dialog.textContent.toLowerCase();

            // Check if this is a proxy auth dialog
            if (dialogText.includes('proxy') ||
                dialogText.includes('authentication') ||
                dialogText.includes('username') ||
                dialogText.includes('password') ||
                dialog.querySelector('input[type="password"]')) {

                console.log('Found proxy auth dialog, attempting to handle...');

                // Try to find and click cancel/close buttons
                const closeButtons = dialog.querySelectorAll([
                    'button[data-action="cancel"]',
                    'button[aria-label*="cancel"]',
                    'button[aria-label*="close"]',
                    'button[type="button"]',
                    '.cancel-btn',
                    '.close-btn',
                    'button'
                ].join(','));

                let buttonClicked = false;
                closeButtons.forEach(button => {
                    const buttonText = button.textContent.toLowerCase();
                    if (buttonText.includes('cancel') ||
                        buttonText.includes('close') ||
                        buttonText.includes('ok') ||
                        button.getAttribute('aria-label')?.toLowerCase().includes('close')) {
                        button.click();
                        console.log('Clicked close/cancel button on auth dialog');
                        buttonClicked = true;
                    }
                });

                // If no button worked, try to hide/remove the dialog
                if (!buttonClicked) {
                    try {
                        dialog.style.display = 'none';
                        dialog.style.visibility = 'hidden';
                        dialog.remove();
                        console.log('Forcibly removed auth dialog');
                    } catch (e) {
                        console.log('Could not remove auth dialog:', e);
                    }
                }
            }
        });

        // Also handle Chrome's built-in auth dialogs by pressing Escape
        if (document.querySelector('input[type="password"]')) {
            console.log('Found password input, pressing Escape to dismiss');
            document.dispatchEvent(new KeyboardEvent('keydown', {
                key: 'Escape',
                code: 'Escape',
                keyCode: 27,
                which: 27,
                bubbles: true
            }));
        }
    }

    // Monitor for auth popups with more aggressive detection
    const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
            if (mutation.addedNodes.length > 0) {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        // Check if this node or its children contain auth elements
                        const hasAuthElements = node.matches && (
                            node.matches('dialog') ||
                            node.matches('[role="dialog"]') ||
                            node.querySelector('input[type="password"]') ||
                            node.querySelector('input[placeholder*="password"]') ||
                            node.querySelector('input[placeholder*="username"]')
                        );

                        const hasAuthText = node.textContent && (
                            node.textContent.toLowerCase().includes('proxy') ||
                            node.textContent.toLowerCase().includes('authentication') ||
                            node.textContent.toLowerCase().includes('sign in') ||
                            node.textContent.toLowerCase().includes('credentials')
                        );

                        if (hasAuthElements || hasAuthText) {
                            console.log('Detected potential auth dialog, handling...');
                            setTimeout(handleProxyAuthDialogs, 100);
                        }
                    }
                });
            }
        });
    });

    // Start observing when DOM is ready
    function startObserving() {
        if (document.body) {
            observer.observe(document.body, {
                childList: true,
                subtree: true,
                attributes: true,
                attributeFilter: ['style', 'class']
            });
            console.log('Started observing for auth dialogs');
        } else {
            setTimeout(startObserving, 100);
        }
    }

    // Initialize
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            handleProxyAuthDialogs();
            startObserving();
        });
    } else {
        handleProxyAuthDialogs();
        startObserving();
    }

    // Periodic check for auth dialogs
    setInterval(handleProxyAuthDialogs, 2000);

    // Handle window focus events (sometimes auth dialogs appear on focus)
    window.addEventListener('focus', () => {
        setTimeout(handleProxyAuthDialogs, 500);
    });

    console.log('Proxy auth content script loaded and active');
})();
