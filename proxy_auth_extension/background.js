// Enhanced background script for automatic proxy authentication
const PROXY_USERNAME = 'PLACEHOLDER_USERNAME';
const PROXY_PASSWORD = 'PLACEHOLDER_PASSWORD';

let authAttempts = 0;
const MAX_AUTH_ATTEMPTS = 10;

console.log('Enhanced Proxy Auth Handler loaded with username:', PROXY_USERNAME);

// Store credentials for immediate access
chrome.storage.local.set({
  proxyUsername: PROXY_USERNAME,
  proxyPassword: PROXY_PASSWORD
});

// Enhanced authentication handler with better error handling
chrome.webRequest.onAuthRequired.addListener(
  function(details) {
    console.log('🔐 Proxy authentication requested for:', details.url);
    console.log('🔍 Challenge details:', details.challenger);
    console.log('📊 Attempt #:', authAttempts + 1);

    authAttempts++;

    if (authAttempts > MAX_AUTH_ATTEMPTS) {
      console.log('❌ Max auth attempts reached, cancelling request');
      return {cancel: true};
    }

    // Provide credentials immediately
    console.log('✅ Providing proxy credentials for:', PROXY_USERNAME);
    return {
      authCredentials: {
        username: PROXY_USERNAME,
        password: PROXY_PASSWORD
      }
    };
  },
  {urls: ["*://*/*"]},
  ["asyncBlocking"]
);

// Handle before request to inject auth headers
chrome.webRequest.onBeforeSendHeaders.addListener(
  function(details) {
    // Add proxy authorization header if needed
    if (details.url.includes('httpbin.org') || details.url.includes('google.com')) {
      console.log('🚀 Adding proxy auth headers for:', details.url);

      const authString = btoa(PROXY_USERNAME + ':' + PROXY_PASSWORD);
      details.requestHeaders.push({
        name: 'Proxy-Authorization',
        value: 'Basic ' + authString
      });

      return {requestHeaders: details.requestHeaders};
    }
  },
  {urls: ["*://*/*"]},
  ["requestHeaders", "extraHeaders"]
);

// Reset auth attempts periodically
setInterval(() => {
  const oldAttempts = authAttempts;
  authAttempts = 0;
  if (oldAttempts > 0) {
    console.log('🔄 Reset auth attempts counter (was:', oldAttempts, ')');
  }
}, 30000); // Reset every 30 seconds

// Enhanced error handling
chrome.webRequest.onErrorOccurred.addListener(
  function(details) {
    if (details.error.includes('PROXY') || details.error.includes('AUTH') ||
        details.error.includes('ERR_PROXY') || details.error.includes('407')) {
      console.log('❌ Proxy error:', details.error, 'for URL:', details.url);
      console.log('🔧 Suggestion: Check proxy server and credentials');
    }
  },
  {urls: ["*://*/*"]}
);

// Track successful requests
chrome.webRequest.onCompleted.addListener(
  function(details) {
    if (details.statusCode === 200 && authAttempts > 0) {
      console.log('✅ Successful request after proxy auth:', details.url, 'Status:', details.statusCode);
      // Reset attempts on success
      authAttempts = 0;
    } else if (details.statusCode === 407) {
      console.log('🔐 Proxy authentication required (407) for:', details.url);
    }
  },
  {urls: ["*://*/*"]}
);

// Handle response headers to check for auth challenges
chrome.webRequest.onHeadersReceived.addListener(
  function(details) {
    if (details.statusCode === 407) {
      console.log('🚨 Proxy Authentication Required (407) detected for:', details.url);
      console.log('📋 Response headers:', details.responseHeaders);
    }
  },
  {urls: ["*://*/*"]},
  ["responseHeaders"]
);

console.log('🎯 Enhanced Proxy Auth Handler extension loaded successfully');
console.log('🔑 Ready to handle proxy authentication for:', PROXY_USERNAME);
