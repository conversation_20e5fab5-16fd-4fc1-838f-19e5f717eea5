// Background script to handle proxy authentication
// This will be dynamically replaced with actual credentials
const PROXY_USERNAME = 'PLACEHOLDER_USERNAME';
const PROXY_PASSWORD = 'PLACEHOLDER_PASSWORD';

let authAttempts = 0;
const MAX_AUTH_ATTEMPTS = 5;

console.log('Proxy Auth Handler extension loaded with credentials:', PROXY_USERNAME);

// Handle authentication requests
chrome.webRequest.onAuthRequired.addListener(
  function(details) {
    console.log('Proxy auth requested for:', details.url);
    console.log('Auth challenge details:', details);
    authAttempts++;

    if (authAttempts > MAX_AUTH_ATTEMPTS) {
      console.log('Max auth attempts reached, cancelling');
      return {cancel: true};
    }

    // Return actual proxy credentials
    console.log('Providing proxy authentication with username:', PROXY_USERNAME);
    return {
      authCredentials: {
        username: PROXY_USERNAME,
        password: PROXY_PASSWORD
      }
    };
  },
  {urls: ["*://*/*"]},
  ["asyncBlocking"]
);

// Reset auth attempts periodically
setInterval(() => {
  authAttempts = 0;
  console.log('Reset auth attempts counter');
}, 60000); // Reset every 60 seconds

// Handle proxy errors
chrome.webRequest.onErrorOccurred.addListener(
  function(details) {
    if (details.error.includes('PROXY') || details.error.includes('AUTH')) {
      console.log('Proxy error occurred:', details.error, 'for URL:', details.url);
    }
  },
  {urls: ["*://*/*"]}
);

// Handle completed requests to track success
chrome.webRequest.onCompleted.addListener(
  function(details) {
    if (details.statusCode === 200 && authAttempts > 0) {
      console.log('Successful request after proxy auth:', details.url);
    }
  },
  {urls: ["*://*/*"]}
);

console.log('Proxy Auth Handler extension loaded successfully');
