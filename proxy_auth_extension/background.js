// Background script to handle proxy authentication
let authAttempts = 0;
const MAX_AUTH_ATTEMPTS = 3;

// Handle authentication requests
chrome.webRequest.onAuthRequired.addListener(
  function(details) {
    console.log('Proxy auth requested for:', details.url);
    authAttempts++;
    
    if (authAttempts > MAX_AUTH_ATTEMPTS) {
      console.log('Max auth attempts reached, cancelling');
      return {cancel: true};
    }
    
    // Return empty credentials to bypass authentication
    console.log('Bypassing proxy authentication');
    return {
      authCredentials: {
        username: '',
        password: ''
      }
    };
  },
  {urls: ["*://*/*"]},
  ["asyncBlocking"]
);

// Reset auth attempts periodically
setInterval(() => {
  authAttempts = 0;
}, 30000); // Reset every 30 seconds

// Handle proxy errors
chrome.webRequest.onErrorOccurred.addListener(
  function(details) {
    if (details.error.includes('PROXY') || details.error.includes('AUTH')) {
      console.log('Proxy error occurred:', details.error);
    }
  },
  {urls: ["*://*/*"]}
);

console.log('Proxy Auth Handler extension loaded successfully');
