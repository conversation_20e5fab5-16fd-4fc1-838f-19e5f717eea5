# 🧪 Individual Proxy Method Testing Guide

## Problem
You want to test each proxy authentication method individually to find which one works without manual prompts.

## 🚀 Quick Testing Commands

### **Test All Methods One by One:**
```bash
python test_individual_methods.py
```
This tests each method individually and shows you which ones work.

### **Test Only One Specific Method:**
```bash
python test_single_method.py 1    # Test Method 1 (Extension)
python test_single_method.py 2    # Test Method 2 (Auth String)
python test_single_method.py 3    # Test Method 3 (Chrome Args)
python test_single_method.py 5    # Test Method 5 (Tunnel)
python test_single_method.py 4    # Test Method 4 (No Proxy)
```

### **Force Main Script to Use Only One Method:**
```bash
# Force Method 1 (Extension)
FORCE_PROXY_METHOD=1 python 2.py

# Force Method 2 (Auth String)  
FORCE_PROXY_METHOD=2 python 2.py

# Force Method 3 (Chrome Args)
FORCE_PROXY_METHOD=3 python 2.py

# Force Method 5 (Tunnel)
FORCE_PROXY_METHOD=5 python 2.py
```

---

## 📋 Available Methods

### **Method 1: Enhanced Chrome Extension**
- **What**: Chrome extension with injected proxy credentials
- **Test**: `python test_single_method.py 1`
- **Force**: `FORCE_PROXY_METHOD=1 python 2.py`

### **Method 2: Authenticated Proxy String**
- **What**: Direct proxy authentication in URL format
- **Test**: `python test_single_method.py 2`
- **Force**: `FORCE_PROXY_METHOD=2 python 2.py`

### **Method 3: Chrome Arguments + Extension**
- **What**: Chrome command line arguments with extension
- **Test**: `python test_single_method.py 3`
- **Force**: `FORCE_PROXY_METHOD=3 python 2.py`

### **Method 5: Local Proxy Tunnel**
- **What**: Local proxy server that handles authentication
- **Test**: `python test_single_method.py 5`
- **Force**: `FORCE_PROXY_METHOD=5 python 2.py`

### **Method 4: No Proxy (Fallback)**
- **What**: Runs without proxy
- **Test**: `python test_single_method.py 4`
- **Force**: `FORCE_PROXY_METHOD=4 python 2.py`

---

## 🎯 What to Look For

### **✅ Success Indicators:**
- No manual username/password dialog appears
- Browser navigates to httpbin.org/ip automatically
- Gets IP response without manual intervention
- Can navigate to Gmail login page

### **❌ Failure Indicators:**
- Browser shows "Enter proxy username and password" dialog
- Page doesn't load or shows error
- Manual intervention required

---

## 📊 Testing Process

### **Step 1: Test Individual Methods**
```bash
python test_single_method.py 1
```
- Watch for manual auth dialogs
- If no dialogs appear and you get IP response = SUCCESS
- If dialogs appear = FAILED

### **Step 2: Test Next Method**
```bash
python test_single_method.py 2
```
Continue until you find a working method.

### **Step 3: Use Working Method**
Once you find a working method (e.g., Method 2), force your main script to use only that method:
```bash
FORCE_PROXY_METHOD=2 python 2.py
```

---

## 🔧 Example Testing Session

```bash
# Test Method 1
$ python test_single_method.py 1
❌ METHOD 1 FAILED - manual auth dialog appeared

# Test Method 2  
$ python test_single_method.py 2
✅ METHOD 2 WORKS! - no manual auth, got IP response

# Use Method 2 for main script
$ FORCE_PROXY_METHOD=2 python 2.py
# Select action 1 for login test
# Should work without manual prompts
```

---

## 🛠️ Troubleshooting

### **If All Methods Fail:**
1. Check proxy server accessibility
2. Verify credentials in Accounts.txt
3. Test proxy manually in browser
4. Contact proxy provider

### **If Method Works in Test but Fails in Main Script:**
1. Use the FORCE_PROXY_METHOD environment variable
2. Check logs in `log/accounts/your_email.txt`
3. Ensure same proxy credentials are used

### **If Extension Methods Fail:**
- Check extension files are created
- Verify credentials are injected
- Look for browser console errors

### **If Tunnel Method Fails:**
- Check if port 8000-9000 range is available
- Verify proxy supports CONNECT method
- Check firewall settings

---

## 💡 Pro Tips

1. **Start with Method 2** - Often the most reliable
2. **Use FORCE_PROXY_METHOD** - Once you find a working method
3. **Test thoroughly** - Wait full 20 seconds to check for auth dialogs
4. **Check logs** - Always check the log files for detailed error info

---

## 🎉 Success!

Once you find a working method, you can:
1. Use `FORCE_PROXY_METHOD=X` for consistent results
2. Run bulk operations without manual intervention
3. Process multiple accounts automatically

**Your proxy authentication will be fully automatic!** 🚀
