#!/usr/bin/env python3
"""
Comprehensive test script for all proxy authentication methods
"""

import sys
import time
import importlib.util
from pathlib import Path

# Import the main script module
def import_main_module():
    spec = importlib.util.spec_from_file_location("main_module", "2.py")
    main_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(main_module)
    return main_module.GmailAutomation

def test_all_proxy_methods():
    """Test all proxy authentication methods"""
    
    print("=" * 80)
    print("🔧 COMPREHENSIVE PROXY AUTHENTICATION TEST")
    print("=" * 80)
    
    # Initialize automation
    basedir = Path(__file__).parent
    GmailAutomation = import_main_module()
    automation = GmailAutomation(basedir)
    
    # Load test account
    accounts_file = basedir / 'Accounts.txt'
    if not accounts_file.exists():
        print("❌ ERROR: Accounts.txt not found!")
        return False
    
    with open(accounts_file, 'r') as f:
        line = f.readline().strip()
        if not line:
            print("❌ ERROR: No accounts found!")
            return False
    
    parts = line.split(',')
    if len(parts) < 7:
        print("❌ ERROR: Invalid account format!")
        return False
    
    account = {
        "email": parts[0],
        "password": parts[1],
        "proxy": parts[2],
        "port": parts[3],
        "proxyUsername": parts[4],
        "proxyPassword": parts[5],
        "recovry": parts[6]
    }
    
    print(f"🧪 Testing account: {account['email']}")
    print(f"🌐 Proxy: {account['proxy']}:{account['port']}")
    print(f"🔑 Auth: {account['proxyUsername']}:{'*' * len(account['proxyPassword'])}")
    print()
    
    # Test each method individually
    methods = [
        ("Method 1: Extension-based", automation._create_driver_method1_extension),
        ("Method 2: Authenticated proxy string", automation._create_driver_method2_auth_proxy),
        ("Method 3: Chrome arguments", automation._create_driver_method3_chrome_args),
        ("Method 5: Local proxy tunnel", automation._create_driver_method5_tunnel),
        ("Method 4: No proxy (fallback)", automation._create_driver_method4_no_proxy)
    ]
    
    successful_methods = []
    profile_dir = basedir / 'profiles' / account['email']
    profile_dir.mkdir(parents=True, exist_ok=True)
    
    for method_name, method_func in methods:
        print(f"🔄 Testing {method_name}...")
        
        try:
            driver = method_func(account, profile_dir)
            
            if driver:
                print(f"  ✅ Driver created successfully")
                
                # Test basic navigation
                try:
                    print(f"  🌐 Testing navigation to httpbin.org...")
                    driver.get('http://httpbin.org/ip')
                    time.sleep(10)  # Wait longer for potential auth dialogs
                    
                    # Check if we got a response
                    page_source = driver.page_source.lower()
                    current_url = driver.current_url
                    
                    print(f"  📍 Current URL: {current_url}")
                    
                    if 'origin' in page_source or 'ip' in page_source:
                        print(f"  ✅ Navigation successful - got IP response")
                        print(f"  🎉 {method_name} WORKS WITHOUT MANUAL AUTH!")
                        successful_methods.append(method_name)
                    elif 'httpbin.org' in current_url:
                        print(f"  ⚠️  Reached httpbin but unclear response")
                        print(f"  📄 Page content preview: {page_source[:200]}...")
                    else:
                        print(f"  ❌ Navigation failed or redirected")
                        
                except Exception as nav_e:
                    print(f"  ❌ Navigation error: {nav_e}")
                
                # Test Gmail navigation
                try:
                    print(f"  🔍 Testing Gmail navigation...")
                    driver.get('https://accounts.google.com')
                    time.sleep(5)
                    
                    current_url = driver.current_url
                    if 'accounts.google.com' in current_url:
                        print(f"  ✅ Gmail navigation successful")
                    else:
                        print(f"  ⚠️  Unexpected URL: {current_url}")
                        
                except Exception as gmail_e:
                    print(f"  ❌ Gmail navigation error: {gmail_e}")
                
                # Close driver
                try:
                    driver.quit()
                    print(f"  ✅ Driver closed successfully")
                except:
                    print(f"  ⚠️  Error closing driver")
                    
            else:
                print(f"  ❌ Driver creation failed")
                
        except Exception as e:
            print(f"  ❌ Method failed with error: {e}")
        
        print()
        time.sleep(2)  # Brief pause between methods
    
    # Summary
    print("=" * 80)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 80)
    
    if successful_methods:
        print(f"🎉 SUCCESS! {len(successful_methods)} method(s) work without manual authentication:")
        for i, method in enumerate(successful_methods, 1):
            print(f"  {i}. {method}")
        
        print(f"\n💡 RECOMMENDATION:")
        print(f"   Use '{successful_methods[0]}' as your primary method")
        print(f"   The script will automatically try methods in order until one works")
        
        print(f"\n🚀 NEXT STEPS:")
        print(f"   1. Run: python 2.py")
        print(f"   2. Select action '1' for login test")
        print(f"   3. The script will use the working method automatically")
        print(f"   4. No manual proxy authentication should be required!")
        
    else:
        print("❌ NO METHODS WORKED AUTOMATICALLY")
        print("\n🔧 TROUBLESHOOTING:")
        print("   1. Verify proxy server is accessible")
        print("   2. Check proxy credentials are correct")
        print("   3. Ensure proxy supports HTTP CONNECT method")
        print("   4. Try different proxy server if available")
        print("   5. Contact proxy provider for support")
    
    print("\n" + "=" * 80)
    
    return len(successful_methods) > 0

if __name__ == "__main__":
    success = test_all_proxy_methods()
    
    if success:
        print("\n🎯 READY FOR BULK AUTOMATION!")
        print("   Your proxy authentication is now fully automatic.")
    else:
        print("\n⚠️  MANUAL INTERVENTION STILL REQUIRED")
        print("   Additional configuration may be needed.")
    
    sys.exit(0 if success else 1)
