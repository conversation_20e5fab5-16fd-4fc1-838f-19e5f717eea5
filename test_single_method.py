#!/usr/bin/env python3
"""
Test a single specific proxy method
Usage: python test_single_method.py [method_number]
"""

import sys
import time
import importlib.util
from pathlib import Path

def import_main_module():
    spec = importlib.util.spec_from_file_location("main_module", "2.py")
    main_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(main_module)
    return main_module.GmailAutomation

def load_account():
    """Load account from Accounts.txt"""
    basedir = Path(__file__).parent
    accounts_file = basedir / 'Accounts.txt'
    
    if not accounts_file.exists():
        print("❌ ERROR: Accounts.txt not found!")
        return None, None
    
    with open(accounts_file, 'r') as f:
        line = f.readline().strip()
        if not line:
            print("❌ ERROR: No accounts found!")
            return None, None
    
    parts = line.split(',')
    if len(parts) < 7:
        print("❌ ERROR: Invalid account format!")
        return None, None
    
    account = {
        "email": parts[0],
        "password": parts[1],
        "proxy": parts[2],
        "port": parts[3],
        "proxyUsername": parts[4],
        "proxyPassword": parts[5],
        "recovry": parts[6]
    }
    
    return account, basedir

def test_single_method(method_num):
    """Test a specific method"""
    
    # Load account
    account, basedir = load_account()
    if not account:
        return False
    
    # Initialize automation
    GmailAutomation = import_main_module()
    automation = GmailAutomation(basedir)
    
    profile_dir = basedir / 'profiles' / account['email']
    profile_dir.mkdir(parents=True, exist_ok=True)
    
    # Define available methods
    methods = {
        1: ("Enhanced Chrome Extension", automation._create_driver_method1_extension),
        2: ("Authenticated Proxy String", automation._create_driver_method2_auth_proxy),
        3: ("Chrome Arguments + Extension", automation._create_driver_method3_chrome_args),
        5: ("Local Proxy Tunnel", automation._create_driver_method5_tunnel),
        4: ("No Proxy (Fallback)", automation._create_driver_method4_no_proxy)
    }
    
    if method_num not in methods:
        print(f"❌ Invalid method number: {method_num}")
        print(f"Available methods: {list(methods.keys())}")
        return False
    
    method_name, method_func = methods[method_num]
    
    print(f"🔧 TESTING SINGLE METHOD")
    print(f"{'='*50}")
    print(f"📧 Account: {account['email']}")
    print(f"🌐 Proxy: {account['proxy']}:{account['port']}")
    print(f"🔑 Auth: {account['proxyUsername']}:{'*' * len(account['proxyPassword'])}")
    print(f"🧪 Method: {method_num} - {method_name}")
    print(f"{'='*50}")
    
    driver = None
    try:
        print(f"🔄 Creating driver with Method {method_num}...")
        driver = method_func(account, profile_dir)
        
        if not driver:
            print(f"❌ Driver creation failed")
            return False
        
        print(f"✅ Driver created successfully")
        
        # Test 1: Basic proxy connection
        print(f"\n🧪 TEST 1: Basic proxy connection")
        print(f"🌐 Navigating to http://httpbin.org/ip...")
        
        driver.get('http://httpbin.org/ip')
        print(f"📍 Navigation completed")
        
        # Wait for potential auth dialogs
        print(f"⏳ Waiting 20 seconds to check for manual auth prompts...")
        print(f"   (If you see a username/password dialog, this method failed)")
        time.sleep(20)
        
        # Check results
        current_url = driver.current_url
        page_source = driver.page_source.lower()
        
        print(f"📍 Current URL: {current_url}")
        
        if 'httpbin.org' in current_url and ('origin' in page_source or 'ip' in page_source):
            print(f"✅ TEST 1 PASSED: Got IP response without manual auth!")
            
            # Extract IP from response
            if '"origin"' in page_source:
                try:
                    import json
                    page_text = driver.page_source
                    if '{' in page_text and '}' in page_text:
                        start = page_text.find('{')
                        end = page_text.rfind('}') + 1
                        json_data = json.loads(page_text[start:end])
                        print(f"🌐 Your IP through proxy: {json_data.get('origin', 'Unknown')}")
                except:
                    pass
            
        else:
            print(f"❌ TEST 1 FAILED: No proper response or manual auth required")
            print(f"📄 Page content preview: {page_source[:300]}...")
            return False
        
        # Test 2: Gmail navigation
        print(f"\n🧪 TEST 2: Gmail navigation")
        print(f"🔍 Navigating to https://accounts.google.com...")
        
        driver.get('https://accounts.google.com')
        time.sleep(10)
        
        gmail_url = driver.current_url
        print(f"📍 Gmail URL: {gmail_url}")
        
        if 'accounts.google.com' in gmail_url:
            print(f"✅ TEST 2 PASSED: Gmail navigation successful")
        else:
            print(f"⚠️ TEST 2 WARNING: Unexpected URL")
        
        # Test 3: Check for auth dialogs
        print(f"\n🧪 TEST 3: Auth dialog check")
        try:
            # Look for common auth dialog indicators
            auth_indicators = [
                "proxy authentication",
                "username",
                "password",
                "sign in to proxy"
            ]
            
            page_text = driver.page_source.lower()
            found_auth = any(indicator in page_text for indicator in auth_indicators)
            
            if found_auth:
                print(f"⚠️ TEST 3 WARNING: Possible auth dialog detected")
            else:
                print(f"✅ TEST 3 PASSED: No auth dialogs detected")
                
        except Exception as e:
            print(f"⚠️ TEST 3 ERROR: {e}")
        
        print(f"\n🎉 METHOD {method_num} TESTING COMPLETE!")
        print(f"✅ This method appears to work automatically")
        
        return True
        
    except Exception as e:
        print(f"❌ Method {method_num} failed with error: {e}")
        return False
        
    finally:
        if driver:
            try:
                driver.quit()
                print(f"🔒 Driver closed")
            except:
                pass

def main():
    if len(sys.argv) != 2:
        print(f"Usage: python test_single_method.py [method_number]")
        print(f"Available methods:")
        print(f"  1 - Enhanced Chrome Extension")
        print(f"  2 - Authenticated Proxy String") 
        print(f"  3 - Chrome Arguments + Extension")
        print(f"  5 - Local Proxy Tunnel")
        print(f"  4 - No Proxy (Fallback)")
        print(f"\nExample: python test_single_method.py 1")
        return False
    
    try:
        method_num = int(sys.argv[1])
    except ValueError:
        print(f"❌ Invalid method number: {sys.argv[1]}")
        return False
    
    success = test_single_method(method_num)
    
    if success:
        print(f"\n🎯 METHOD {method_num} WORKS!")
        print(f"You can now use this method for bulk automation.")
    else:
        print(f"\n❌ METHOD {method_num} FAILED")
        print(f"Try a different method number.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
