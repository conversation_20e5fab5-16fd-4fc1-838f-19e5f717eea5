#!/usr/bin/env python3
"""
Test each proxy method individually
"""

import sys
import time
import importlib.util
from pathlib import Path

def import_main_module():
    spec = importlib.util.spec_from_file_location("main_module", "2.py")
    main_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(main_module)
    return main_module.GmailAutomation

def load_account():
    """Load account from Accounts.txt"""
    basedir = Path(__file__).parent
    accounts_file = basedir / 'Accounts.txt'
    
    if not accounts_file.exists():
        print("❌ ERROR: Accounts.txt not found!")
        return None, None
    
    with open(accounts_file, 'r') as f:
        line = f.readline().strip()
        if not line:
            print("❌ ERROR: No accounts found!")
            return None, None
    
    parts = line.split(',')
    if len(parts) < 7:
        print("❌ ERROR: Invalid account format!")
        return None, None
    
    account = {
        "email": parts[0],
        "password": parts[1],
        "proxy": parts[2],
        "port": parts[3],
        "proxyUsername": parts[4],
        "proxyPassword": parts[5],
        "recovry": parts[6]
    }
    
    return account, basedir

def test_method(method_num, method_name, method_func, account, automation, profile_dir):
    """Test a specific method"""
    print(f"\n{'='*60}")
    print(f"🧪 TESTING METHOD {method_num}: {method_name}")
    print(f"{'='*60}")
    
    try:
        print(f"🔄 Creating driver...")
        driver = method_func(account, profile_dir)
        
        if not driver:
            print(f"❌ Driver creation failed")
            return False
        
        print(f"✅ Driver created successfully")
        
        # Test proxy connection
        print(f"🌐 Testing proxy connection...")
        try:
            driver.get('http://httpbin.org/ip')
            print(f"📍 Navigated to httpbin.org/ip")
            
            # Wait and check for manual auth dialogs
            print(f"⏳ Waiting 15 seconds to check for manual auth prompts...")
            time.sleep(15)
            
            # Check current URL and page content
            current_url = driver.current_url
            page_source = driver.page_source.lower()
            
            print(f"📍 Current URL: {current_url}")
            
            if 'httpbin.org' in current_url and ('origin' in page_source or 'ip' in page_source):
                print(f"🎉 SUCCESS! Method {method_num} works WITHOUT manual authentication!")
                print(f"✅ Got IP response automatically")
                
                # Test Gmail navigation
                print(f"🔍 Testing Gmail navigation...")
                driver.get('https://accounts.google.com')
                time.sleep(5)
                
                gmail_url = driver.current_url
                if 'accounts.google.com' in gmail_url:
                    print(f"✅ Gmail navigation successful")
                    print(f"🎯 METHOD {method_num} IS FULLY WORKING!")
                else:
                    print(f"⚠️ Gmail navigation issue: {gmail_url}")
                
                return True
                
            else:
                print(f"❌ Method {method_num} failed - no proper response")
                print(f"📄 Page content preview: {page_source[:200]}...")
                return False
                
        except Exception as nav_e:
            print(f"❌ Navigation error: {nav_e}")
            return False
            
    except Exception as e:
        print(f"❌ Method {method_num} failed: {e}")
        return False
        
    finally:
        try:
            if 'driver' in locals() and driver:
                driver.quit()
                print(f"🔒 Driver closed")
        except:
            pass

def main():
    print("🔧 INDIVIDUAL PROXY METHOD TESTER")
    print("This will test each method one by one to find which works")
    print()
    
    # Load account
    account, basedir = load_account()
    if not account:
        return False
    
    print(f"📧 Account: {account['email']}")
    print(f"🌐 Proxy: {account['proxy']}:{account['port']}")
    print(f"🔑 Auth: {account['proxyUsername']}:{'*' * len(account['proxyPassword'])}")
    
    # Initialize automation
    GmailAutomation = import_main_module()
    automation = GmailAutomation(basedir)
    
    profile_dir = basedir / 'profiles' / account['email']
    profile_dir.mkdir(parents=True, exist_ok=True)
    
    # Define methods to test
    methods = [
        (1, "Enhanced Chrome Extension", automation._create_driver_method1_extension),
        (2, "Authenticated Proxy String", automation._create_driver_method2_auth_proxy),
        (3, "Chrome Arguments + Extension", automation._create_driver_method3_chrome_args),
        (5, "Local Proxy Tunnel", automation._create_driver_method5_tunnel),
    ]
    
    working_methods = []
    
    # Test each method
    for method_num, method_name, method_func in methods:
        success = test_method(method_num, method_name, method_func, account, automation, profile_dir)
        
        if success:
            working_methods.append((method_num, method_name))
            print(f"\n🎉 METHOD {method_num} WORKS! Adding to working methods list.")
        else:
            print(f"\n❌ METHOD {method_num} FAILED")
        
        # Pause between tests
        print(f"\n⏳ Waiting 3 seconds before next test...")
        time.sleep(3)
    
    # Results
    print(f"\n{'='*80}")
    print(f"📊 FINAL RESULTS")
    print(f"{'='*80}")
    
    if working_methods:
        print(f"🎉 SUCCESS! Found {len(working_methods)} working method(s):")
        for method_num, method_name in working_methods:
            print(f"  ✅ Method {method_num}: {method_name}")
        
        best_method = working_methods[0]
        print(f"\n💡 RECOMMENDATION:")
        print(f"   Use Method {best_method[0]}: {best_method[1]}")
        
        print(f"\n🚀 NEXT STEPS:")
        print(f"   1. Your main script will automatically use Method {best_method[0]}")
        print(f"   2. Run: python 2.py")
        print(f"   3. Select action '1' for login test")
        print(f"   4. No manual authentication should be required!")
        
        return True
    else:
        print(f"❌ NO METHODS WORKED AUTOMATICALLY")
        print(f"\n🔧 TROUBLESHOOTING:")
        print(f"   1. Check if proxy server {account['proxy']}:{account['port']} is accessible")
        print(f"   2. Verify credentials: {account['proxyUsername']}:{account['proxyPassword']}")
        print(f"   3. Test proxy manually in browser")
        print(f"   4. Contact proxy provider for support")
        
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print(f"\n🎯 READY FOR AUTOMATION!")
    else:
        print(f"\n⚠️ NEEDS MANUAL CONFIGURATION")
    
    sys.exit(0 if success else 1)
